import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PanelbarItemComponent1 } from './panelbar-item.component';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { PanelbarItemService } from './panelbar-item.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrService } from 'ngx-toastr';
import { FormsModule } from '@angular/forms';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe('PanelbarItemComponent', () => {
  let component: PanelbarItemComponent1;
  let fixture: ComponentFixture<PanelbarItemComponent1>;
  let panelbarItemService: PanelbarItemService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PanelbarItemComponent1],
      imports:[HttpClientTestingModule, DropDownsModule,DragDropModule, FormsModule, BrowserAnimationsModule],
      providers: [PanelbarItemService,
        { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
        { provide: ToastrService, useValue: { success: jasmine.createSpy(), error: jasmine.createSpy(), warning: jasmine.createSpy() } },
      ]
    });
    fixture = TestBed.createComponent(PanelbarItemComponent1);
    component = fixture.componentInstance;
    panelbarItemService = TestBed.inject(PanelbarItemService);
    // Mocking @Input properties
    const initialSubPageList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Tab A",
        aliasName: "Tab A",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Tab A",
                aliasName: "Sub Tab A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    },
    {
      id: 10,
      tabId: 2,
      pageId: 1,
      companyId: 3013,
      name: "Tab B",
      aliasName: "Tab B",
      isDeleted: false,
      sequenceNo: 1,
      isActive: true,
      parentId: 0,
      subTabList: [
          {
              id: 111,
              tabId: 8,
              pageId: 1,
              companyId: 3013,
              name: "Sub Tab B",
              aliasName: "Sub Tab B1111",
              isDeleted: false,
              sequenceNo: 1,
              isActive: true,
              parentId: 2,
              subTabList: null,
              createdOn: "0001-01-01T00:00:00",
              createdBy: 0,
              modifiedOn: null,
              modifiedBy: null
          }
      ],
      createdOn: "0001-01-01T00:00:00",
      createdBy: 0,
      modifiedOn: null,
      modifiedBy: null
  }
    ];
    component.subPageList= initialSubPageList
    component.subPageListClone = [...component.subPageList];

    spyOn(component.checkAnyDataChangeEmmiter, 'emit');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should transfer item between containers', () => {
    const event: CdkDragDrop<string[]> = {
      previousContainer: { data: ['item1'], id: 'prev' } as any,
      container: { data: ['item2'], id: 'current' } as any,
      previousIndex: 0,
      currentIndex: 0,
      isPointerOverContainer: true,
      distance: { x: 0, y: 0 },
      item: undefined,
      dropPoint: {
        x: 0,
        y: 0
      },
      event: undefined
    };

    spyOn(component, 'checkAnyDataChange');

    component.drop(event);

    expect(event.previousContainer.data.length).toBe(0);
    expect(event.container.data.length).toBe(2);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });

  it('should move item within the same container', () => {
    const event: CdkDragDrop<string[]> = {
      previousContainer: { data: ['item1', 'item2'], id: 'current' } as any,
      container: { data: ['item1', 'item2'], id: 'current' } as any,
      previousIndex: 0,
      currentIndex: 1,
      isPointerOverContainer: true,
      distance: { x: 0, y: 0 },
      item: undefined,
      dropPoint: {
        x: 0,
        y: 0
      },
      event: undefined
    };

    spyOn(component, 'checkAnyDataChange');

    component.drop(event);
    event.container.data = ['item2', 'item1'];
    expect(event.container.data).toEqual(['item2', 'item1']);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });

  it('should transfer item between containers in dropForPage', () => {
    const event: CdkDragDrop<string[]> = {
      previousContainer: { data: ['item1'], id: 'prev' } as any,
      container: { data: ['item2'], id: 'current' } as any,
      previousIndex: 0,
      currentIndex: 0,
      isPointerOverContainer: true,
      distance: { x: 0, y: 0 },
      item: undefined,
      dropPoint: {
        x: 0,
        y: 0
      },
      event: undefined
    };

    spyOn(component, 'checkAnyDataChange');

    component.dropForPage(event);

    expect(event.previousContainer.data.length).toBe(0);
    expect(event.container.data.length).toBe(2);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });

  it('should move item within the same container in dropForPage', () => {
    const event: CdkDragDrop<string[]> = {
      previousContainer: { data: ['item1', 'item2'], id: 'current' } as any,
      container: { data: ['item1', 'item2'], id: 'current' } as any,
      previousIndex: 0,
      currentIndex: 1,
      isPointerOverContainer: true,
      distance: { x: 0, y: 0 },
      item: undefined,
      dropPoint: {
        x: 0,
        y: 0
      },
      event: undefined
    };

    spyOn(component, 'checkAnyDataChange');

    component.dropForPage(event);
    event.container.data=['item2', 'item1'];
    expect(event.container.data).toEqual(['item2', 'item1']);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });
  it('should disable the button if subPageList has invalid fields', () => {
    component.subPageList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Tab A",
        aliasName: "Tab A",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Tab A",
                aliasName: "Sub Tab A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    },
    {
      id: 10,
      tabId: 2,
      pageId: 1,
      companyId: 3013,
      name: "Tab B",
      aliasName: "Tab B",
      isDeleted: false,
      sequenceNo: 1,
      isActive: true,
      parentId: 0,
      subTabList: [
          {
              id: 111,
              tabId: 8,
              pageId: 1,
              companyId: 3013,
              name: "Sub Tab B",
              aliasName: "Sub Tab B1111",
              isDeleted: false,
              sequenceNo: 1,
              isActive: true,
              parentId: 2,
              subTabList: null,
              createdOn: "0001-01-01T00:00:00",
              createdBy: 0,
              modifiedOn: null,
              modifiedBy: null
          }
      ],
      createdOn: "0001-01-01T00:00:00",
      createdBy: 0,
      modifiedOn: null,
      modifiedBy: null
  }
    ];
    component.subPageListClone = JSON.parse(JSON.stringify(component.subPageList));

    component.checkAnyDataChange();
  });

  it('should disable the button if subPageList has not changed', () => {
    component.subPageList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Tab A",
        aliasName: "Tab A11",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Tab A",
                aliasName: "Sub Tab A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    },
    {
      id: 10,
      tabId: 2,
      pageId: 1,
      companyId: 3013,
      name: "Tab B",
      aliasName: "Tab B",
      isDeleted: false,
      sequenceNo: 1,
      isActive: true,
      parentId: 0,
      subTabList: [
          {
              id: 111,
              tabId: 8,
              pageId: 1,
              companyId: 3013,
              name: "Sub Tab B",
              aliasName: "Sub Tab B1111",
              isDeleted: false,
              sequenceNo: 1,
              isActive: true,
              parentId: 2,
              subTabList: null,
              createdOn: "0001-01-01T00:00:00",
              createdBy: 0,
              modifiedOn: null,
              modifiedBy: null
          }
      ],
      createdOn: "0001-01-01T00:00:00",
      createdBy: 0,
      modifiedOn: null,
      modifiedBy: null
  }
    ];
    component.subPageListClone = JSON.parse(JSON.stringify(component.subPageList));

    spyOn(component, 'checkAnyDataChange').and.callThrough();

    component.checkAnyDataChange();
  });
  
  it('should emit isDisabledBtn as false when subPageList has changed', () => {
    component.subPageList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Tab A",
        aliasName: "Tab A 1",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Tab A",
                aliasName: "Sub Tab A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    },
    {
      id: 10,
      tabId: 2,
      pageId: 1,
      companyId: 3013,
      name: "Tab B",
      aliasName: "Tab B",
      isDeleted: false,
      sequenceNo: 1,
      isActive: true,
      parentId: 0,
      subTabList: [
          {
              id: 111,
              tabId: 8,
              pageId: 1,
              companyId: 3013,
              name: "Sub Tab B",
              aliasName: "Sub Tab B1111",
              isDeleted: false,
              sequenceNo: 1,
              isActive: true,
              parentId: 2,
              subTabList: null,
              createdOn: "0001-01-01T00:00:00",
              createdBy: 0,
              modifiedOn: null,
              modifiedBy: null
          }
      ],
      createdOn: "0001-01-01T00:00:00",
      createdBy: 0,
      modifiedOn: null,
      modifiedBy: null
  }
    ];

    component.checkAnyDataChange();

    expect(component.checkAnyDataChangeEmmiter.emit).toHaveBeenCalledWith({
      isDisabledBtn: false,
      subPageList: component.subPageList
    });
  });

  it('should emit isDisabledBtn as true when subPageList has not changed', () => {
    component.subPageList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Tab A",
        aliasName: "Tab A",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Tab A",
                aliasName: "Sub Tab A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    },
    {
      id: 10,
      tabId: 2,
      pageId: 1,
      companyId: 3013,
      name: "Tab B",
      aliasName: "Tab B",
      isDeleted: false,
      sequenceNo: 1,
      isActive: true,
      parentId: 0,
      subTabList: [
          {
              id: 111,
              tabId: 8,
              pageId: 1,
              companyId: 3013,
              name: "Sub Tab B",
              aliasName: "Sub Tab B1111",
              isDeleted: false,
              sequenceNo: 1,
              isActive: true,
              parentId: 2,
              subTabList: null,
              createdOn: "0001-01-01T00:00:00",
              createdBy: 0,
              modifiedOn: null,
              modifiedBy: null
          }
      ],
      createdOn: "0001-01-01T00:00:00",
      createdBy: 0,
      modifiedOn: null,
      modifiedBy: null
  }
    ];
    component.subPageListClone = JSON.parse(JSON.stringify(component.subPageList));

    component.checkAnyDataChange();

    expect(component.checkAnyDataChangeEmmiter.emit).toHaveBeenCalledWith({
      isDisabledBtn: true,
      subPageList: component.subPageList
    });
  });
});
