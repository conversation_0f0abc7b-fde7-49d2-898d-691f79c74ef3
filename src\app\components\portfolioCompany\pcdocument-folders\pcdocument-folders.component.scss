@import "../../../../variables.scss";

// Spacing
$spacing-xs: 4px;
$spacing-sm: 6px;
$spacing-md: 8px;
$spacing-lg: 12px;
$spacing-xl: 20px;
$spacing-xxl: 40px;
$space-44: 44px;
$space-48: 48px;

// Colors
$color-gray-text: #374151;
$color-gray-icon: #6B7280;
$color-selected-bg: #EBF3FF;
$border-color: $Neutral-Gray-10;

// Tree indentation
$tree-level-indent: 20px;

.folder-list-container {

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: $Neutral-Gray-02;
    padding: $Spacing-8 $Spacing-12;
    height: $space-48;
  }

  .search-container {
    display: flex;
    align-items: center;
    border-bottom: 1px solid $Neutral-Gray-10;
    border-top: 1px solid $Neutral-Gray-10;
    height: $spacing-xxl;

    input[type="text"] {
      border: none;
      padding: 0.5rem;
      margin-left: 0.5rem;
      width: 100%;
    }

    .search-icon {
      height: $Spacing-12;
      width: $Spacing-12;
    }
  }

  .nodata-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: $Neutral-Gray-00;
    text-align: center;
    height: calc(100vh - 267px);
    border-bottom-left-radius: $Radius-8;

    img {
      display: block;
      max-width: 100%;
      height: auto;
    }
  }

  .tree-container {
    background: $Neutral-Gray-00;
    height: calc(100vh - 267px);
    overflow-y: auto;
    border-bottom-left-radius: $Radius-8;
  }

  .tree-list {
    list-style: none;
    padding-left: 0;
    margin: 0;
  }

  .tree-item {
    .tree-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      padding: $spacing-sm $spacing-lg $spacing-sm 0;
      height: $spacing-xxl;
      border-bottom: 1px solid $border-color;

      .pl-44{
        padding-left: $space-44 !important;
      }

      .node-data {
        &[data-level] {
          padding-left: calc(var(--level, 0) * $tree-level-indent);
        }
      }

      &.selected {
        background-color: $color-selected-bg;
      }
      
      .icon {
        width: $spacing-xl;
        color: $color-gray-icon;
        margin-left: $spacing-md;
        margin-right: $spacing-md;
      }

      .arrow-icon {
        padding-left: $spacing-lg;
      }

      .item-name {
        color: $color-gray-text;
      }
    }
  }

  .tree-children {
    .tree-item {
      .tree-children .tree-item .tree-item-content {
        padding-left: $tree-level-indent;
      }
    }
  }
}