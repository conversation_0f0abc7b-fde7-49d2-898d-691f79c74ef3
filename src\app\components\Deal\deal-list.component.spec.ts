import { ComponentFixture, TestBed } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA,ChangeDetectorRef,ElementRef} from "@angular/core";
import { NgxSpinnerService } from "ngx-spinner";
import { LazyLoadEvent } from "primeng/api";
import { AccountService } from "../../services/account.service";
import { DealService } from "../../services/deal.service";
import { MiscellaneousService } from "../../services/miscellaneous.service";
import { FeaturesEnum } from "../../services/permission.service";
import { DealDetailsConstants } from "../../common/constants";
import { FormsModule } from "@angular/forms";
import { DealListComponent } from "./deal-list.component";
import { MatMenuModule } from "@angular/material/menu";
import { of } from "rxjs";
import { HttpHeaders, HttpResponse } from "@angular/common/http";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { ToastrModule, ToastrService } from "ngx-toastr";
import { KendoService } from "src/app/services/kendo.service";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";

describe("DealListComponent", () => {
  let component: DealListComponent;
  let fixture: ComponentFixture<DealListComponent>;
  let kendoServiceStub: Partial<KendoService>;
  let subPermissionServiceSpy: jasmine.SpyObj<CommonSubFeaturePermissionService>;
  const commonServiceSpy = jasmine.createSpyObj('CommonSubFeaturePermissionService', ['getCommonFeatureAccessPermissions']);
  beforeEach(async () => {
    const subPermissionServiceStub = jasmine.createSpyObj('SubPermissionService', ['getCommonFeatureAccessPermissions']);
    kendoServiceStub = {
      getHeaderValue: () => ( { params: "any", parentSort: "any" }),
    };
    const changeDetectorRefStub = () => ({ detectChanges: () => ({}) });
    const elementRefStub = () => ({});
    const ngxSpinnerServiceStub = () => ({});
    const accountServiceStub = () => ({});
    const dealServiceStub = () => ({
      getDealsQuery: () => of([]),
      exportDealNewTransaction: () => of([]),
      exportDealList: () => of([])
    });
    const commonServiceStub = () => ({
      getCommonSubFeatureAccessPermissions: () => of([]),
      getCommonFeatureAccessPermissions: () => of([])
    });
    const miscellaneousServiceStub = () => ({
      getPagerLength: () => ({}),
      downloadExcelFile: response => ({}),
      GetPaginatorEvent: elementRef => ({})
    });
    await TestBed.configureTestingModule({
      imports: [FormsModule, MatMenuModule, HttpClientTestingModule,ToastrModule.forRoot()],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [DealListComponent],
      providers: [
        {
          provide: CommonSubFeaturePermissionService,
          useValue: subPermissionServiceStub,
        },
        { provide: KendoService, useValue: kendoServiceStub },
        { provide: ChangeDetectorRef, useFactory: changeDetectorRefStub },
        { provide: ElementRef, useFactory: elementRefStub },
        { provide: NgxSpinnerService, useFactory: ngxSpinnerServiceStub },
        { provide: AccountService, useFactory: accountServiceStub },
        { provide: DealService, useFactory: dealServiceStub },
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub },
        ToastrService,
        { provide: 'ToastConfig', useValue: {} }
      ]
    })
    .compileComponents();
    
    subPermissionServiceSpy = TestBed.inject(CommonSubFeaturePermissionService) as jasmine.SpyObj<CommonSubFeaturePermissionService>;
    subPermissionServiceSpy.getCommonFeatureAccessPermissions.and.returnValue(of([])); // Mock the method
    fixture = TestBed.createComponent(DealListComponent);
    component = fixture.componentInstance;
  });

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`feature has default value`, () => {
    expect(component.feature).toEqual(FeaturesEnum);
  });

  it(`blockedTable has default value`, () => {
    expect(component.blockedTable).toEqual(false);
  });

  it(`dealData has default value`, () => {
    expect(component.dealData).toEqual([]);
  });

  it(`isLoader has default value`, () => {
    expect(component.isLoader).toEqual(false);
  });

  it(`isExportLoading has default value`, () => {
    expect(component.isExportLoading).toEqual(false);
  });

  it(`dealDetailsConstants has default value`, () => {
    expect(component.dealDetailsConstants).toEqual(DealDetailsConstants);
  });

  it(`isdownloadfilter has default value`, () => {
    expect(component.isdownloadfilter).toEqual(true);
  });

  it(`show has default value`, () => {
    expect(component.show).toEqual(false);
  });

  it(`disableConfirm has default value`, () => {
    expect(component.disableConfirm).toEqual(false);
  });

  it(`newInvestmentsNotFound has default value`, () => {
    expect(component.newInvestmentsNotFound).toEqual(false);
  });

  it('#exportDealNewInvestment should call exportDealNewTransaction and downloadExcelFile', () => {
    const year = '2022';
    const quarter = 'Q1';
    const response: HttpResponse<Blob> = new HttpResponse<Blob>({ body: null, headers: new HttpHeaders() });
    const dealService = TestBed.inject(DealService);
    const miscService = TestBed.inject(MiscellaneousService);
    spyOn(miscService, 'downloadExcelFile').and.callThrough();
    spyOn(dealService, 'exportDealNewTransaction').and.returnValue(of(response));

    component.exportDealNewInvestment(year, quarter);

    expect(dealService.exportDealNewTransaction).toHaveBeenCalledWith({
      paginationFilter: jasmine.any(Object), // Replace with actual expected object
      includeAllDetails: true,
      year: year,
      quarter: quarter
    });
    expect(miscService.downloadExcelFile).toHaveBeenCalledWith(response);
  });

  it('#downloadNewInvestment should call exportDealNewInvestment with correct parameters', () => {
    spyOn(component, 'exportDealNewInvestment');

    const year = '2022';
    const quarter = 'Q1';
    component.year = year;
    component.quarter = quarter;

    component.downloadNewInvestment();

    expect(component.exportDealNewInvestment).toHaveBeenCalledWith(year, quarter);
  });

  it('#setHeaderName should set item in localStorage', () => {
    const dealName = 'Test Deal';
    spyOn(localStorage, 'setItem');

    component.setHeaderName(dealName);

    expect(localStorage.setItem).toHaveBeenCalledWith('headerName', dealName);
  });

  it('#openQuarterYearSelectionPopup should set properties to correct values', () => {
    component.openQuarterYearSelectionPopup();

    expect(component.year).toBeNull();
    expect(component.quarter).toBeNull();
    expect(component.YearQuarter).toBeNull();
    expect(component.newInvestmentsNotFound).toBeFalse();
    expect(component.disableConfirm).toBeFalse();
    expect(component.show).toBeFalse();
  });

  it('#closeQuarterYearSelectionPopup should set properties to correct values', () => {
    component.closeQuarterYearSelectionPopup();
  
    expect(component.year).toBeNull();
    expect(component.quarter).toBeNull();
    expect(component.YearQuarter).toBeNull();
    expect(component.newInvestmentsNotFound).toBeFalse();
    expect(component.disableConfirm).toBeTrue();
    expect(component.show).toBeFalse();
  });

  it('#QuarterYear should set properties to correct values', () => {
    const event = { quarter: 'Q1', year: '2022' };
  
    component.QuarterYear(event);
  
    expect(component.YearQuarter).toEqual('Q1 2022');
    expect(component.year).toEqual('2022');
    expect(component.quarter).toEqual('Q1');
    expect(component.newInvestmentsNotFound).toBeFalse();
    expect(component.disableConfirm).toBeFalse();
  });
});
