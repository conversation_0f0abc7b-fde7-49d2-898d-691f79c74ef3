<div id="fund-details-resizeable" class="row mr-0 ml-0 fund-detail-section pb-1" [ngClass]="loading?'d-none':'d-block'"
    (resized)="onResized($event)">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <div class="row mr-0 ml-0 header-section pb-2">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header fund-static-title TextTruncate custom-fund-left-width"
                    title="{{model.fundName}}">
                    {{model.fundName}}
                </div>
                <div class="float-right custom-fund-right-width">
                    <div class="float-right">
                        <div class="fund-splitButton d-inline-block">
                            <kendo-splitbutton *ngIf="mappedLpReportTemplates?.length>0"
                                (buttonClick)="openSplitButton($event)" #splitButton
                                (itemClick)="downloadReport($event)"
                                class="split-button-custom-primary-bg split-button-custom"
                                [ngClass]="exportLoading ? 'split-button-width-164' : 'split-button-width-140'"
                                buttonClass="split-button-custom-bg" [data]="mappedLpReportTemplates"
                                [popupSettings]="{ popupClass:'popup-split-button-custom-primary-bg', animate: true,popupAlign: { vertical: 'bottom', horizontal: 'left' } }"
                                id="fund-download-btn">
                                Download <img *ngIf="exportLoading" alt="" src="assets/dist/images/Loader.svg"
                                    class=" spin float-right">
                                <ng-template kendoSplitButtonItemTemplate let-dataItem>
                                    <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 pr-0 pl-0 ">
                                        <div class="float-left pr-1"> <kendo-svg-icon [icon]="pdfIcon"
                                                class="pdf-icon "></kendo-svg-icon>
                                        </div>
                                        <div class="">
                                            <span class="truncate Body-R" [fillMode]="'outline'" [size]="'medium'"
                                                [themeColor]="'primary'" title="{{dataItem.templateName}}"
                                                id="{{ dataItem.templateName }}">
                                                {{ dataItem.templateName }}
                                            </span>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-splitbutton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 card card-main static-card pb-4">
            <div class="col-12 col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0">
                    <div class="col-12  pr-0 pl-0">
                        <div class="row mr-0 ml-0 pb-1 static-bg">
                            <div class="col-12 pr-0 pl-0 chart-title pc-section-header">
                                <div class="float-right pr-2 fund-static-header">
                                    <a id="fund-details-investor-edit" (click)="editRedirect()"
                                        [hideIfUnauthorized]='{featureId:feature.Fund,action:"edit"}' title="Edit"><img
                                            alt="" src="assets/dist/images/EditIcon.svg"></a>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="canViewFundDetails && fundStaticConfiguartionData?.length>0">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate"
                                        title="{{fundStaticDataTitle?.displayName}}">{{fundStaticDataTitle?.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                            </div>
                            <div class="pl-3 pr-3 Fund-section col-12">
                                <div class="row mr-0 ml-0">
                                    <ng-container *ngFor="let fund of fundStaticConfiguartionData">
                                        <div class="col-4 pr-0 pl-0 pt-2 mb-1 custom-fund-label-padding"
                                            *ngIf="fund.hasLink  && fund.name !== 'StrategyDescription' && fund.name !== 'FundSize' && fund.name != 'Investors'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{fund.displayName}}"><span
                                                            class="hColor static-label TextTruncate d-block">
                                                            {{fund.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-3 col-8 custom-fund-value-padding ">
                                                    <div class="TextTruncate">

                                                        <a id="fund-details-click-view" title="{{fund.value}}"
                                                            class="linkStyle"
                                                            [routerLink]="['/firm-details', model?.firmDetail?.encryptedFirmID]"
                                                            class="click-view"
                                                            href="javascript:void(0);">{{fund.value}}</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 pl-0 custom-fund-label-padding"
                                            *ngIf="!fund.hasLink  && fund.name !== 'StrategyDescription' && fund.name !== 'FundSize' && fund.name != 'Investors'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{fund.displayName}}" class=""><span
                                                            class="hColor static-label TextTruncate  d-block">
                                                            {{fund.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 col-8 pl-3 custom-fund-value-padding">
                                                    <div class="TextTruncate">
                                                        <span id="{{fund.fieldID}}" title="{{fund.value||'NA'}}"
                                                            class="vColor static-field-value"
                                                            *ngIf="fund.name != companyInformationConstants.Customfield;else other_content;">{{fund.value||
                                                            "NA"}}
                                                        </span>
                                                    </div>
                                                    <ng-template #other_content>
                                                        <span *ngIf="fund.dataTypeId!=mDataTypes.List">
                                                            <span id="{{fund.fieldID}}" title="{{fund.value||'NA'}}"
                                                                *ngIf="fund.dataTypeId==mDataTypes.FreeText||fund.dataTypeId==0||fund.dataTypeId==mDataTypes.Date">
                                                                {{fund.value||"NA"}}
                                                            </span>
                                                            <span id="{{fund.fieldID}}"
                                                                *ngIf="fund.dataTypeId==mDataTypes.CurrencyValue"
                                                                [innerHtml]="isNumberCheck(fund.value) ? (fund.value | number:NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : fund.value||'NA'">
                                                            </span>
                                                            <span id="{{fund.fieldID}}"
                                                                *ngIf="fund.dataTypeId==mDataTypes.Number"
                                                                [innerHtml]="isNumberCheck(fund.value) ? (fund.value | number:NumberDecimalConst.noDecimal | minusSignToBrackets: '') : fund.value||'NA'">
                                                            </span>
                                                            <span id="{{fund.fieldID}}"
                                                                *ngIf="fund.dataTypeId==mDataTypes.Multiple">
                                                                {{isNumberCheck(fund.value)
                                                                ? (fund.value | number:
                                                                NumberDecimalConst.multipleDecimal)+'x'
                                                                : fund.value||"NA"}}
                                                            </span>
                                                            <span id="{{fund.fieldID}}"
                                                                *ngIf="fund.dataTypeId==mDataTypes.Percentage"
                                                                [innerHtml]="isNumberCheck(fund.value) ? (fund.value | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%') : fund.value||'NA'">
                                                            </span>
                                                        </span>
                                                    </ng-template>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 pl-0 custom-fund-label-padding"
                                            *ngIf="fund.name == 'FundSize'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{fund.displayName}}" class="TextTruncate"><span
                                                            class="hColor TextTruncate static-label  d-block">
                                                            {{fund.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-0 col-8 pl-3 TextTruncate custom-fund-value-padding"
                                                    *ngIf="fund.name == 'FundSize'">
                                                    <div title="{{(fund.value |number : NumberDecimalConst.currencyDecimal)|| 'NA'}}"
                                                        class="TextTruncate">
                                                        <span
                                                            class="vColor static-field-value TextTruncate">{{fund.value!='NA'?(fund.value
                                                            |number : NumberDecimalConst.currencyDecimal)||
                                                            "NA":"NA"}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 pl-0 custom-fund-label-padding"
                                            *ngIf="fund.name == 'Investors'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{fund.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label TextTruncate d-block">
                                                            {{fund.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-0 col-8 pl-3 custom-fund-value-padding">
                                                    <div title="investors" class="TextTruncate">
                                                        <div *ngIf="investorList.length > 1"
                                                            class="investor-link TextTruncate"
                                                            [matMenuTriggerFor]="menuInvestor"
                                                            title=">{{investorList[0]?.investorName}} + {{investorList.length -1}}">
                                                            {{investorList[0]?.investorName}} + {{investorList.length
                                                            -1}} </div>
                                                        <div *ngIf="investorList.length == 0"
                                                            class="investor-link invest-NA TextTruncate">NA</div>
                                                        <div id="fund-details-investor-menu-redirect"
                                                            *ngIf="investorList.length == 1"
                                                            class="investor-link TextTruncate"
                                                            (click)="redirectToInvestor(investorList[0])"
                                                            title="{{investorList[0]?.investorName}} ">
                                                            {{investorList[0]?.investorName}} </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="canViewFundDetails && stratedyDesc?.displayName!=undefined">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate"
                                        title="{{stratedyDesc?.displayName}}">{{stratedyDesc?.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                                <div class="chart-content">
                                    <div class=" statit-desc pr-0 pl-0 pt-3 TextTruncate"
                                        title="{{model?.strategyDescription|| 'NA'}}">{{model?.strategyDescription||
                                        "NA"}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="canViewFundTerms && fundTermsConfiguartionData?.length>0">
                            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate"
                                        title="{{fundTermsTitle?.displayName}}">{{fundTermsTitle?.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pl-3 pb-2">
                                <div class="row mr-0 ml-0">
                                    <ng-container *ngFor="let fundTerm of fundTermsConfiguartionData ">
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='TargetCommitment'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 col-6 custom-fund-value-padding">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate"
                                                            title="{{(model.targetCommitment|number : NumberDecimalConst.currencyDecimal)||'NA'}}">
                                                            {{(model.targetCommitment|number :
                                                            NumberDecimalConst.currencyDecimal)||"NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='MaximumCommitment'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 col-6 custom-fund-value-padding">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate"
                                                            title="{{(model.maximumCommitment|number : NumberDecimalConst.currencyDecimal)||'NA'}}">{{(model.maximumCommitment|number
                                                            : NumberDecimalConst.currencyDecimal)||"NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='GPCommitment'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate" title=" {{(model.gpCommitment|number : NumberDecimalConst.percentDecimal)||
                                                        'NA'}}">{{(model.gpCommitment|number :
                                                            NumberDecimalConst.percentDecimal)||
                                                            "NA"}}
                                                        </span><span *ngIf="model.gpCommitment"> %</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='PreferredReturnPercent'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate" title="{{(model.preferredReturnPercent|number : NumberDecimalConst.percentDecimal)||
                                                        'NA'}}">{{(model.preferredReturnPercent|number :
                                                            NumberDecimalConst.percentDecimal)||
                                                            "NA"}}</span><span *ngIf="model.preferredReturnPercent">
                                                            %
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='CarriedInterestPercent'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate" title="{{(model.carriedInterestPercent|number : NumberDecimalConst.percentDecimal)||
                                                        'NA'}">{{(model.carriedInterestPercent|number :
                                                            NumberDecimalConst.percentDecimal)||
                                                            "NA"}}</span><span *ngIf="model.carriedInterestPercent">
                                                            %
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='GPCatchupPercent'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate" title="{{(model.gpCatchupPercent|number : NumberDecimalConst.percentDecimal)||
                                                        'NA'}}">{{(model.gpCatchupPercent|number :
                                                            NumberDecimalConst.percentDecimal)||
                                                            "NA"}}</span><span *ngIf="model.gpCatchupPercent">%
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='ManagementFee'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor TextTruncate static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate" title="{{(model.managementFee|number : NumberDecimalConst.percentDecimal)||
                                                        'NA'}}">{{(model.managementFee|number :
                                                            NumberDecimalConst.percentDecimal)||
                                                            "NA"}}
                                                        </span> <span *ngIf="model.managementFee"> %</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='ManagementFeeOffset'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label ">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span
                                                            class="vColor static-field-value TextTruncate">{{(model.managementFeeOffset|number
                                                            : NumberDecimalConst.currencyDecimal)||
                                                            "NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='FundTerm'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="vColor static-field-value TextTruncate">
                                                        <span class="TextTruncate" title="{{(model.fundTerm|number : NumberDecimalConst.singleDecimal)||
                                                        'NA'}}">{{(model.fundTerm|number :
                                                            NumberDecimalConst.singleDecimal)||
                                                            "NA"}}</span><span *ngIf="model.fundTerm">
                                                            year(s)</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='MaximumExtensionToFundTerm'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="vColor static-field-value TextTruncate">
                                                        <span class="TextTruncate"
                                                            title="{{(model.maximumExtensionToFundTerm|number : NumberDecimalConst.singleDecimal)|| 'NA'}}">{{(model.maximumExtensionToFundTerm|number
                                                            : NumberDecimalConst.singleDecimal)|| "NA"}}</span><span
                                                            *ngIf="model.maximumExtensionToFundTerm"> year(s)</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='FundClosingDate'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor TextTruncate static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span
                                                            class="vColor static-field-value TextTruncate">{{(model.fundClosingDate
                                                            | date:'MM/dd/yyyy')||
                                                            "NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='OrgExpenses'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate"
                                                            title="{{(model.orgExpenses|number : NumberDecimalConst.currencyDecimal)|| 'NA'}}">{{(model.orgExpenses|number
                                                            : NumberDecimalConst.currencyDecimal)|| "NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='Clawback'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate"
                                                            title="{{model.clawback|| 'NA'}}">{{model.clawback|| "NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-fund-label-padding"
                                            *ngIf="fundTerm.name=='Customfield'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-6">
                                                    <div title="{{fundTerm.displayName}}" class="TextTruncate"><span
                                                            class="hColor static-label TextTruncate">
                                                            {{fundTerm.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 custom-fund-value-padding col-6">
                                                    <div class="TextTruncate">
                                                        <span class="vColor static-field-value TextTruncate"
                                                            title="{{fundTerm.value || 'NA'}}">{{fundTerm.value ||
                                                            "NA"}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="canViewGeoLoc && fundLocationConfiguartionData?.length>0">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate"
                                        title="{{fundLocationDataTitle?.displayName}}">{{fundLocationDataTitle?.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                            </div>
                            <div class="col-12 pr-0 pl-0 pt-2">
                                <div class="card">
                                    <div class="card-body mb-0">
                                        <div class="table-responsive card-border">
                                            <table class='table mb-0 static-info-table'>
                                                <thead>
                                                    <tr>
                                                        <ng-container *ngFor="let geo of fundLocationConfiguartionData">
                                                            <th scope="col" class="text-align-left"
                                                                title="{{geo.displayName}}">{{geo.displayName}}</th>
                                                        </ng-container>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <ng-container *ngFor="let loc of fundLocationConfiguartionData">
                                                            <td class="text-align-left">
                                                                <span
                                                                    *ngIf="loc.name ==companyInformationConstants.Region"
                                                                    title="{{model.geographyDetail?.region?.region || 'NA'}}">{{model.geographyDetail?.region?.region
                                                                    || "NA"}}</span>
                                                                <span
                                                                    *ngIf="loc.name ==companyInformationConstants.Country"
                                                                    title="{{model.geographyDetail?.country?.country || 'NA'}}">{{model.geographyDetail?.country?.country
                                                                    || "NA"}}</span>
                                                                <span
                                                                    *ngIf="loc.name ==companyInformationConstants.State"
                                                                    title="{{model.geographyDetail?.state?.state || 'NA'}}">{{model.geographyDetail?.state?.state
                                                                    || "NA"}}</span>
                                                                <span
                                                                    *ngIf="loc.name ==companyInformationConstants.City"
                                                                    title="{{model.geographyDetail?.city?.city || 'NA'}}">
                                                                    {{model.geographyDetail?.city?.city || "NA"}}
                                                                </span>
                                                            </td>
                                                        </ng-container>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <app-empty-state [isGraphImage]="false"
                                                *ngIf="fundLocationConfiguartionData?.length==0"></app-empty-state>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 header-section header-performance">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header pb-1">
                    <div class="row mr-0 ml-0 header-section">
                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                            <div class="float-left fund-header pb-2 TextTruncate" title="Fund Performance">
                                Fund Performance
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 fund-performance-content chart-area">
                <div class="chart-content pt-3">
                    <app-multiline-point-chart *ngIf="fundPerformanceData!=undefined" [isDisplay]="width"
                        [yFontSize]="'14px'" [xFontSize]="'14px'" [markerFontSize]="'12px'" [height]="340"
                        [data]="fundPerformanceData" [xColumn]="'ValuationDate'"
                        [yObjects]="{'Gross TVPI':{column:'Gross TVPI'},'Gross IRR':{column:'Gross IRR'},'Net TVPI':{column:'Net TVPI'},'Net IRR':{column:'Net IRR'}}"
                        [axisLabels]="{ xAxis: 'Quarters', yAxis: 'IRR', yAxisRight: 'TVPI' }">
                    </app-multiline-point-chart>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 header-performance">
            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xl-6 pr-0 pl-0">
                <div class="row ml-0">
                    <div class="col-12 pr-0 pl-0">
                        <div class="float-left fund-header pb-2 TextTruncate" title="Sector Wise Total Value">
                            Sector Wise Total Value
                        </div>
                    </div>
                </div>
                <div class="row section-chart ml-0 border-1">
                    <div class="col-12  chart-area">
                        <div class="chart-content col-sm-8 m-sm-auto" *ngIf="sectorwiseHoldingValues">
                            <app-donut-chart [isDisplay]="width" [height]="400" [unit]="''" [catField]="'Sector'"
                                [valField]="'Total Value'" [data]="sectorwiseHoldingValues"
                                [title]="'Total Value'"></app-donut-chart>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xl-6 pr-0 pl-0">
                <div class="row  mr-0 ml-0">
                    <div class="col-12 pr-0 pl-0">
                        <div class="float-left fund-header pb-2 TextTruncate" title="Sector Wise Investment Cost">
                            Sector Wise Investment Cost
                        </div>
                    </div>
                </div>
                <div class="row section-chart mr-0 ml-0 border-1">
                    <div class="col-12 chart-area ">
                        <div class="chart-content col-sm-8 m-sm-auto" *ngIf="sectorwiseHoldingValues">
                            <app-donut-chart [height]="400" [isDisplay]="width" [unit]="''" [catField]="'Sector'"
                                [valField]="'Investment Cost'" [data]="sectorwiseHoldingValues"
                                [title]="'Investment Cost'"></app-donut-chart>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 header-performance">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="Deals">
                    Deals
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="row mr-0 ml-0 deal-section">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                        <div class="add-user-component">
                            <div class="card card-main">
                                <div class="card-header card-header-main p-0">
                                    <div class="row mr-0 ml-0 fundlist-header">
                                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                            <div class="float-right">
                                                <div class="d-inline-block search">
                                                    <span class="fa fa-search fasearchicon p-1"></span>
                                                    <input (input)="searchGrid($event.target.value)"
                                                        class="search-text-company companyListSearchHeight TextTruncate"
                                                        placeholder="Search" type="text" [(ngModel)]="globalFilter"
                                                        pInputText />
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="card-body">
                                    <kendo-grid [kendoGridBinding]="deals" [sortable]="true" [selectable]="true"
                                        scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                        class="custom-kendo-pc-list-grid k-grid-border-right-width k-grid-outline-none kendo-fund-deal-grid">
                                        <kendo-grid-column field="portfolioCompanyDetails.companyName"
                                            title="Company Name">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span class="TextTruncate S-M">Company Name</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-deal>
                                                <a id="fund-details-porfolio-company" class="click-view TextTruncate"
                                                    href="javascript:void(0);"
                                                    [routerLink]="['/portfolio-company-detail', deal.portfolioCompanyDetails.encryptedPortfolioCompanyId]"
                                                    title="View Details">{{
                                                    deal.portfolioCompanyDetails.companyName }}</a>
                                            </ng-template>
                                        </kendo-grid-column>
                                        <kendo-grid-column field="dealCustomID" title="Deal">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span class="TextTruncate S-M">Deal</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-deal>
                                                <div id="fund-details-encrypted-deal"
                                                    class="float-left TextTruncate deal-cell"><a class="click-view"
                                                        href="javascript:void(0);"
                                                        [routerLink]="['/deal-details', deal.encryptedDealID]"
                                                        title="View Details">{{deal.dealCustomID}}</a></div>
                                                <div class="float-right">
                                                    <a class="deal-edit"
                                                        [routerLink]="['/save-deal', deal.encryptedDealID]">
                                                        <kendo-svg-icon [icon]="pencilIcon"></kendo-svg-icon>
                                                    </a>
                                                </div>
                                            </ng-template>
                                        </kendo-grid-column>
                                        <ng-template kendoGridNoRecordsTemplate>
                                            <app-empty-state class="finacials-beta-empty-state"
                                                [isGraphImage]="false"></app-empty-state>
                                        </ng-template>
                                    </kendo-grid>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 header-performance" *ngIf="canViewTrackRecord">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="{{headerText}}  ">
                    {{headerText}}
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section">
                <div class="financial-page">
                    <div class="panel panel-default border-0 pt-2 tab-bg">
                        <div class="panel-heading">
                            <div class="panel-title custom-tabs">
                                <ul class="nav nav-tabs ">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link nav-custom active TextTruncate" title="Fund currency"
                                            id="home-tab" data-bs-toggle="tab" type="button" role="tab"
                                            aria-controls="home" aria-selected="true">
                                            Fund currency
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="filter-bg">
                        <div id="fund-details-close-pop-up">
                            <app-trackrecords (onClosePopUpClick)="closeSavePopup($event)"
                                [canExportTrackRecord]="canExportTrackRecord" [canEditTrackRecord]="canEditTrackRecord"
                                (onPickedHeaderText)="getTrackRecordText($event)"></app-trackrecords>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fund Ingestion Section -->
        <div class="row mr-0 ml-0 header-performance mb-4" *ngIf="IsDemoOrTrial">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="{{headerTextFundIngestion}}  ">
                    {{headerTextFundIngestion}}
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section">
                <div class="financial-page">
                    <div class="panel panel-default border-0 pt-2 tab-bg">
                        <div class="panel-heading">
                            <div class="panel-title custom-tabs">
                                <ul class="nav nav-tabs ">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link nav-custom active TextTruncate" title="Fund currency"
                                            id="home-tab" data-bs-toggle="tab" type="button" role="tab"
                                            aria-controls="home" aria-selected="true">
                                            Fund currency
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="filter-bg">
                        <div id="fund-ingestion">
                            <app-fund-ingestion [fundId]="model.fundID"
                                (onPickedHeaderTextFundIngestion)="getFundIngestionText($event)"></app-fund-ingestion>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Fund Ingestion Section End-->

        <!-- Fund Key Performance Indicator Section -->
        <div class="row performance-section pt-3 mr-0 ml-0" *ngIf="tabList?.length>0">
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0">
                <div class="performance-header pt-0 pb-2 TextTruncate">
                    {{fundFinalcialsName}}
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0 border-1">
            <div
                class="tab-section-div panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
                <nav mat-tab-nav-bar [tabPanel]="tabPanel" *ngIf="tabList?.length > 0" id="fundFinancialkpi-tab-container">
                    <a id="{{tab?.tabAliasName}}" mat-tab-link [disableRipple]="true" *ngFor="let tab of tabList;"
                        (click)="selectTab(tab)" [active]="tab.active" class="TextTruncate"
                        title="{{tab?.tabAliasName}}">
                        {{tab?.tabAliasName}} </a>
                </nav>
            </div>
            <div class="content-bg">
                <mat-tab-nav-panel #tabPanel>
                    <div *ngIf="tabName?.includes('Fund Financials')">
                        <app-fund-financials-kpi *ngIf="model && model.fundID" [fundId]="model.fundID"
                            [modelList]="model" [subSectionFields]="fundFinancialsConfigData.kpiConfigurationData"
                            [moduleId]="moduleId"></app-fund-financials-kpi>
                    </div>
                </mat-tab-nav-panel>
            </div>
        </div>
        <div class="row performance-section pt-3 mr-0 ml-0" *ngIf="fundKpiTabs?.length>0">
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0">
                <div class="performance-header pt-0 pb-2 TextTruncate">
                    {{fundKpisName}}
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0 border-1">
            <div
                class="tab-section-div panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
                <nav mat-tab-nav-bar [tabPanel]="fundkpitabPanel" *ngIf="fundKpiTabs?.length > 0"
                    id="fundkpi-tab-container">
                    <a id="{{tab?.tabAliasName}}" mat-tab-link [disableRipple]="true" *ngFor="let tab of fundKpiTabs;"
                        (click)="selectFundKpiTab(tab)" [active]="tab.active" class="TextTruncate"
                        title="{{tab?.tabAliasName}}">
                        {{tab?.tabAliasName}} </a>
                </nav>
            </div>
            <div class="content-bg">
                <mat-tab-nav-panel #fundkpitabPanel>
                    <div *ngIf="fundKpiTabName?.includes('Fund Key Financials')">
                        <app-fund-kpi *ngIf="model && model.fundID" [fundId]="model.fundID" [modelList]="model"
                            [subSectionFields]="fundKpiConfigData.kpiConfigurationData"
                            [moduleId]="fundKpiModuleId"></app-fund-kpi>
                    </div>
                </mat-tab-nav-panel>
            </div>
        </div>
        <!-- Fund Key Performance Indicator Section End -->


    </div>
</div>

<div id="fund-detail-resized" (resized)="onResized($event)">
    <div style="width: 1280px;position: fixed; visibility: hidden;">
        <div *ngFor="let data of reportGraphData;let i=index"
            id="{{data.featureName}}///{{data.featureid}}//{{data.order}}" #button style="position: relative;">
            <div style="width:100%;display:inline-block;" *ngIf="(data.featureName=='Fund Performance Chart')">
                <app-multiline-point-chart [width]="1280" *ngIf="data?.data[0].Results.length>0;else templateName"
                    [data]="data?.data[0].Results" [xColumn]="'ValuationDate'"
                    [yObjects]="{'Gross TVPI':{column:'Gross TVPI'},'Gross IRR':{column:'Gross IRR'},'Net TVPI':{column:'Net TVPI'},'Net IRR':{column:'Net IRR'}}"
                    [axisLabels]="{ xAxis: 'Quarters', yAxis: 'IRR', yAxisRight: 'TVPI' }">
                </app-multiline-point-chart>
                <ng-template #templateName>
                    <div style="text-align: center;" class="text-info mt-3">
                        No record found
                    </div>
                </ng-template>
            </div>
            <div *ngIf="(data.featureName!='Fund Performance Chart')">
                <div>
                    <h4 class="pdf-h4 TextTruncate" title="Attribution
                    {{data?.data[0]?.ReportType!=null?data?.data[0]?.ReportType:data?.ReportType}}">Attribution
                        {{data?.data[0]?.ReportType!=null?data?.data[0]?.ReportType:data?.ReportType}}</h4>
                    <div class="pdf-border-orange" style="border-bottom: 3px solid #f26524;"></div>
                    <div
                        [ngStyle]="(data?.data[0]?.strategies!=''||data?.data[0]?.regions!=''||data?.data[0]?.countries!=''||data?.data[0]?.evaluationDate!='') ? { 'padding': '20px 20px 20px 0px' }: { 'padding': '0px 0px 0px 0px' }">
                        <div *ngIf="data?.data[0]?.strategies!=''"><span
                                style="font-weight: bold;color: #212121;">Strategies:</span> <span
                                style="font-weight:normal;color: #212121;"> {{data?.data[0]?.strategies}}</span></div>
                        <div *ngIf="data?.data[0]?.regions!=''"><span
                                style="font-weight: bold;color: #212121;">Regions:</span>
                            <span style="font-weight:normal;color: #212121;"> {{data?.data[0]?.regions}}</span>
                        </div>
                        <div *ngIf="data?.data[0]?.countries!=''"><span
                                style="font-weight: bold;color: #212121;">Countries:</span><span
                                style="font-weight:normal;color: #212121;"> {{data?.data[0]?.countries}}</span> </div>
                        <div *ngIf="data?.data[0]?.evaluationDate!=''"><span
                                style="font-weight: bold;color: #212121;">Evaluation
                                Date:</span>
                            <span style="font-weight:normal;color: #212121;"> {{data?.data[0]?.evaluationDate |date:
                                'dd/MM/yyyy'}}</span>
                        </div>
                    </div>
                    <div style="display: inline-block; width: 100%;">
                        <div class="rounded-corner-div" style=" display: inline-block;"
                            *ngFor="let graphValue of data?.data[0]?.graphOrder|slice:0:1;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 20px 16px;font-size: 24px;">{{
                                    (graphValue === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue === '# of Investments' ? "Number of Investments" : graphValue )}}</div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('Capital Invested')>=0||(graphValue.indexOf('Unrealized Value')>=0)||(graphValue.indexOf('Realized Value')>=0)||(graphValue.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue"
                                        [data]="data?.data[0]?.Results" [title]="graphValue"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('TVPI')>=0||graphValue.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue"
                                        [valueSuffix]="graphValue.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                        <div class="another-side-rounded-corner-div" style=" display: inline-block; "
                            *ngFor="let graphValue1 of data?.data[0]?.graphOrder | slice:1:2;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 16px 16px;font-size: 24px;">{{
                                    (graphValue1 === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue1 === '# of Investments' ? "Number of Investments" : graphValue1 )
                                    }}</div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('Capital Invested')>=0||(graphValue1.indexOf('Unrealized Value')>=0)||(graphValue1.indexOf('Realized Value')>=0)||(graphValue1.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue1"
                                        [data]="data?.data[0]?.Results" [title]="graphValue1"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('TVPI')>=0||graphValue1.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue1"
                                        [valueSuffix]="graphValue1.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display: inline-block; width: 100%;">
                        <div class="rounded-corner-div" style=" display: inline-block; "
                            *ngFor="let graphValue of data?.data[0]?.graphOrder | slice:2:3;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 16px 16px;font-size: 24px;">
                                    {{(graphValue === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue === '# of Investments' ? "Number of Investments" : graphValue )}}</div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('Capital Invested')>=0||(graphValue.indexOf('Unrealized Value')>=0)||(graphValue.indexOf('Realized Value')>=0)||(graphValue.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue"
                                        [data]="data?.data[0]?.Results" [title]="graphValue"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('TVPI')>=0||graphValue.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue"
                                        [valueSuffix]="graphValue.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                        <div class="another-side-rounded-corner-div" style=" display: inline-block;"
                            *ngFor="let graphValue1 of data?.data[0]?.graphOrder | slice:3:4;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 16px 16px;font-size: 24px;">{{
                                    (graphValue1 === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue1 === '# of Investments' ? "Number of Investments" : graphValue1 )}}</div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('Capital Invested')>=0||(graphValue1.indexOf('Unrealized Value')>=0)||(graphValue1.indexOf('Realized Value')>=0)||(graphValue1.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue1"
                                        [data]="data?.data[0]?.Results" [title]="graphValue1"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('TVPI')>=0||graphValue1.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue1"
                                        [valueSuffix]="graphValue1.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display: inline-block; width: 100%;">
                        <div class="rounded-corner-div" style=" display: inline-block; "
                            *ngFor="let graphValue of data?.data[0]?.graphOrder | slice:4:5;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 16px 16px;font-size: 24px;">{{
                                    (graphValue === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue === '# of Investments' ? "Number of Investments" : graphValue )}}</div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('Capital Invested')>=0||(graphValue.indexOf('Unrealized Value')>=0)||(graphValue.indexOf('Realized Value')>=0)||(graphValue.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue"
                                        [data]="data?.data[0]?.Results" [title]="graphValue"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue.indexOf('TVPI')>=0||graphValue.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue"
                                        [valueSuffix]="graphValue.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                        <div class="another-side-rounded-corner-div" style=" display: inline-block; "
                            *ngFor="let graphValue1 of data?.data[0]?.graphOrder | slice:5:6;"
                            [ngStyle]="{'width': data?.data[0]?.Results.length>0 ? 'none' : '45%' }">
                            <div class="nobreak">
                                <div class="graphsTittle" style="padding: 16px 0 16px 16px;font-size: 24px;">{{
                                    (graphValue1 === 'TVPI' ? "Total value Paid in (TVPI)" :
                                    graphValue1 === '# of Investments' ? "Number of Investments" : graphValue1 )}}</div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('Capital Invested')>=0||(graphValue1.indexOf('Unrealized Value')>=0)||(graphValue1.indexOf('Realized Value')>=0)||(graphValue1.indexOf('Total Value')>=0))">
                                    <app-donut-chart-report [showLegends]=true
                                        *ngIf="data?.data[0]?.Results.length>0;else templateName1" [unit]="'M'"
                                        [catField]="data?.data[0]?.Columns[0]" [valField]="graphValue1"
                                        [data]="data?.data[0]?.Results" [title]="graphValue1"></app-donut-chart-report>
                                    <ng-template #templateName1>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                                <div class=""
                                    *ngIf="(graphValue1.indexOf('TVPI')>=0||graphValue1.indexOf('# of Investments')>=0)">
                                    <app-bar-chart-report *ngIf="data?.data[0]?.Results.length>0;else TVPIvalue"
                                        [data]="data?.data[0]?.Results" [xField]="data?.data[0]?.Columns[0]"
                                        [yField]="graphValue1"
                                        [valueSuffix]="graphValue1.indexOf('TVPI')>=0?'x':''"></app-bar-chart-report>
                                    <ng-template #TVPIvalue>
                                        <div style="text-align: center;" class="text-info mt-3">
                                            No record found
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<mat-menu #menuInvestor="matMenu" backdropClass="investor-menu">
    <div class="custom-investor-menu">
        <div class="investor-menu-header">
            <div class="float-left invest-header TextTruncate" title="Investors">
                Investors
            </div>
            <div class="float-right">
                <img id="fund-details-investor-menu" class="close-img" (click)="toggleMenu(menuInvestor)"
                    src="assets/dist/images/Clear Grey.svg" />
            </div>
        </div>
        <div class="investor-list">
            <button mat-menu-item class="custom-invest-btn" *ngFor="let investor of investorList">
                <span id="fund-details-investor-redirect" title="{{investor?.investorName}}"
                    class="invest-link TextTruncate" (click)="redirectToInvestor(investor)">
                    {{investor?.investorName}}</span>
            </button>
        </div>
    </div>
</mat-menu>