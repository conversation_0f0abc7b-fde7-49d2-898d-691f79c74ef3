{"name": "beat-foliosure-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "coverage": "ng test --karma-config=karma.conf.js --code-coverage --no-watch --browsers=ChromeHeadless", "lint": "ng lint", "e2e": "ng e2e", "sonar": "sonar-scanner"}, "private": true, "dependencies": {"@angular-devkit/architect": "^0.1402.11", "@angular/animations": "^16.1.2", "@angular/cdk": "^16.1.2", "@angular/common": "^16.1.2", "@angular/compiler": "^16.1.2", "@angular/core": "^16.1.2", "@angular/forms": "^16.1.2", "@angular/material": "^16.1.2", "@angular/platform-browser": "^16.1.2", "@angular/platform-browser-dynamic": "^16.1.2", "@angular/router": "^16.1.2", "@highcharts/map-collection": "^2.1.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@microsoft/signalr": "^8.0.7", "@ng-bootstrap/ng-bootstrap": "^15.0.0", "@ng-idle/core": "^14.0.0", "@ng-idle/keepalive": "^14.0.0", "@progress/kendo-angular-conversational-ui": "^14.0.1", "@progress/kendo-angular-dateinputs": "^14.0.1", "@progress/kendo-angular-grid": "^14.0.1", "@progress/kendo-angular-inputs": "^14.0.1", "@progress/kendo-angular-label": "^14.0.1", "@progress/kendo-angular-layout": "^14.0.1", "@progress/kendo-angular-listbox": "^14.0.1", "@progress/kendo-angular-listview": "^14.0.1", "@progress/kendo-angular-menu": "^14.0.1", "@progress/kendo-angular-notification": "^14.0.1", "@progress/kendo-angular-pivotgrid": "^14.0.1", "@progress/kendo-angular-ripple": "^14.0.1", "@progress/kendo-angular-sortable": "^14.0.1", "@progress/kendo-angular-toolbar": "^14.0.1", "@progress/kendo-angular-tooltip": "^14.0.1", "@progress/kendo-angular-treelist": "^14.0.1", "@progress/kendo-angular-typography": "^14.0.1", "@progress/kendo-angular-utils": "^14.0.1", "@progress/kendo-font-icons": "^2.1.0", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-theme-default": "^7.2.0", "@types/codemirror": "^5.60.7", "angular-ng-autocomplete": "^2.0.12", "angular-resize-event": "^3.2.0", "crypto-js": "^4.2.0", "font-awesome": "^4.7.0", "highcharts": "^11.0.1", "install": "^0.13.0", "material-design-icons": "^3.0.1", "mathjs": "^11.8.2", "mini-css-extract-plugin": "^2.7.6", "moment": "^2.30.1", "ng-circle-progress": "^1.7.1", "ng2-search-filter": "^0.5.1", "ngx-idle-timeout": "^1.0.0", "ngx-quill": "^22.0.0", "ngx-spinner": "^16.0.2", "ngx-toastr": "^17.0.2", "oidc-client-ts": "^2.4.0", "popper.js": "^1.16.1", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primeng": "^16.3.1", "quill": "^1.3.7", "rxjs": "~7.8.0", "rxjs-observable": "^0.0.7", "sass-loader": "^13.3.1", "topojson": "^3.0.2", "tslib": "^2.5.3", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^16.0.0", "@angular-devkit/build-angular": "^16.0.5", "@angular-devkit/build-ng-packagr": "^0.1002.0", "@angular-eslint/builder": "16.0.3", "@angular-eslint/eslint-plugin": "16.0.3", "@angular-eslint/eslint-plugin-template": "16.0.3", "@angular-eslint/schematics": "16.0.3", "@angular-eslint/template-parser": "16.0.3", "@angular/cli": "^16.1.1", "@angular/compiler-cli": "^16.1.2", "@types/d3": "^7.4.0", "@types/file-saver": "^2.0.5", "@types/jasmine": "~4.3.0", "@types/node": "^20.2.5", "@types/topojson": "^3.2.3", "@typescript-eslint/eslint-plugin": "5.59.9", "@typescript-eslint/parser": "5.59.9", "codelyzer": "^6.0.2", "codemirror": "^6.0.1", "d3": "^7.8.5", "eslint": "8.42.0", "file-saver": "^2.0.5", "husky": "^8.0.3", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "^7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ng2-pdfjs-viewer": "^15.0.0", "protractor": "^7.0.0", "rxjs-compat": "^6.6.7", "sonar-scanner": "^3.1.0", "ts-node": "^10.9.1", "tslint": "^6.1.3", "typescript": "~5.0.2", "webpack-bundle-analyzer": "^4.9.0", "webpack-dev-server": "^4.15.0"}}