import { AfterViewInit, Component, EventEmitter, Input, OnInit, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { AccountService } from "src/app/services/account.service";
import {DecimalDigitEnum,ExportTypeEnum,FinancialValueUnitsEnum,MiscellaneousService,OrderTypesEnum,PeriodTypeQuarterEnum,} from "src/app/services/miscellaneous.service";
import { FeaturesEnum, UserSubFeaturesEnum, ActionsEnum , PermissionService } from "src/app/services/permission.service";
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DatePipe } from '@angular/common';
import { Observable, Subject, Subscription } from 'rxjs';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { AuditService } from "src/app/services/audit.service";
import { Table } from 'primeng/table';
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { NumberDecimalConst, FinancialsSubTabs, PeriodTypeFilterOptions, PeriodType, FinancialsValueTypes, ImpactKPIConstants, GlobalConstants } from "src/app/common/constants";
import { isNil } from 'src/app/utils/utils';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { FundService } from 'src/app/services/funds.service';

@Component({
  selector: 'app-fund-financials-kpi',
  templateUrl: './fund-financials-kpi.component.html',
  styleUrls: ['./fund-kpi.component.scss']
})
export class FundFinancialsKpiComponent implements OnInit, AfterViewInit, OnChanges {
  NumberDecimalConst = NumberDecimalConst;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  exportType: typeof ExportTypeEnum = ExportTypeEnum;
  searchFilter: any = null;
  eventsSubscription: Subscription;
  @Input() events: Observable<void>;
  @Input() fundId: any;
  @Input() modelList: any;
  @Input() subSectionFields: [];
  msgTimeSpan: number;
  loading = false;
  fundKpiValueUnit: any;
  globalFilter: string = "";
  tableReload = false;
  isLoader: boolean = false;
  frozenCols: any = [{ field: "KPI", header: "KPI" }];
  modelMasterKpi: any = {};
  financialKpiSearchFilter: any;
  financialKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  unitOfCurrency: string = FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions];
  @ViewChild('dt') dt: Table | undefined;
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  infoUpdate: boolean = false;
  ErrorNotation: boolean = false;
  isToasterMessage = false;
  @ViewChild('menu') uiuxMenu!: MatMenu;
  @ViewChild('masterMenuTrigger') menuTrigger: MatMenuTrigger;
  exportMasterKPILoading: boolean = false;
  isValueUpdated: boolean = false;
  tabValueTypeList: ITab[] = [];
  IsPageLoad: boolean = true;
  tabName: string = "";
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  filterOptions: any[] = [];
  tableColumns = [];
  tableFrozenColumns = [];
  tableResult = [];
  tableResultClone = [];
  kpiFilterCols: any = [];
  auditLogList: any = [];
  isToggleChecked: boolean = false;
  isYtd: boolean = false;
  isLtm: boolean = false;
  hasYtd: boolean = false;
  hasLtm: boolean = false;
  isYtdPageLoad: boolean = true;
  isLtmPageLoad: boolean = true;
  pageConfigResponse = { kpiConfigurationData: [], hasChart: false, kpiType: "" };
  defaultType: string = "Monthly";
  isUploadPopupVisible: boolean = false;
  valueTypeString: string;
  kpiCurrencyFilterModel: any = {};
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;
  currencyCode: string = "";
  @Input() moduleId: any;
  constructor(
    private miscService: MiscellaneousService,
    private portfolioCompanyService: PortfolioCompanyService,
    private toastrService: ToastrService,
    private fundService: FundService
  ) {
    this.modelMasterKpi.periodType = {
      type: PeriodTypeQuarterEnum.Last1Year,
    };
    this.modelMasterKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelMasterKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    };

    this.fundKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }

  ngAfterViewInit() {
    if (this.uiuxMenu != undefined) {
      (this.uiuxMenu as any).closed = this.uiuxMenu.closed
      this.configureMenuClose(this.uiuxMenu.closed);
    }
  }
  configureMenuClose(old: MatMenu['closed']): MatMenu['closed'] {
    return old;
  }
  ngOnInit() {
    this.getValueTypeTabList();
    this.currencyCode = this.modelList.currencyDetail.currencyCode;
    this.getFundMasterKPIValues(null);
    this.msgTimeSpan = this.miscService.getMessageTimeSpan();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['moduleId']) {
      this.getFundMasterKPIValues(null);
    }
  }
  
  isNumberCheck(str: any) {
    return isNumeric(str);
  }

  getFundKPIValues(searchFilter: any, event: any) {
    this.financialKpiSearchFilter = searchFilter;
    this.fundService
      .getFundKPIValues({
        fundId: this.fundId,
        paginationFilter: event,
        searchFilter: searchFilter,
        valueType:
          this.valueTypeString != undefined
            ? this.valueTypeString
            : this.tabValueTypeList?.length == 0
              ? ImpactKPIConstants.Actual
              : this.tabName,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: this.IsPageLoad,
        moduleId: this.moduleId,
        isYtdPageLoad: this.isYtdPageLoad,
        isLtmPageLoad: this.isLtmPageLoad,
        isYtd: this.isYtd,
        isLtm: this.isLtm,
        IsSpotRate : false,
        SpotRateDate:null,
        kpiConfigurationData:this.subSectionFields
      })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.loading = false;
            this.ErrorNotation = false;
            this.isLoader = false;
            this.tableReload = true;
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.auditLogList = result?.companyKpiAuditLog || [];
            this.tableResultClone = result?.rows || [];
            this.convertUnits();
            this.kpiFilterCols = [...this.tableFrozenColumns, ...this.tableColumns];
            this.IsPageLoad = false;
            if (this.isYtd) this.isYtdPageLoad = false;
            if (this.isLtm) this.isLtmPageLoad = false;
            if (result != null) {
              this.isMonthly = result?.isMonthly;
              this.isQuarterly = result?.isQuarterly;
              this.isAnnually = result?.isAnnually;
              this.SetFilterOptionsKeys(result);
            }
          } else {
            this.clearData();
          }
        },
        error: (error) => {
          this.clearData();
        }
      });
  }

  private SetFilterOptionsKeys(result: any) {
    this.filterOptions?.forEach(element => {
      switch (element.field) {
        case PeriodTypeFilterOptions.Monthly:
          element.key = result?.isMonthly;
          break;
        case PeriodTypeFilterOptions.Quarterly:
          element.key = result?.isQuarterly;
          break;
        case PeriodTypeFilterOptions.Annual:
          element.key = result?.isAnnually;
          break;
      }
    });
    this.setDefaultTypeTab();
  }

  getFundMasterKPIValues(event: any) {
    this.isLoader = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "FinancialKPI.KPI",
        multiSortMeta: this.financialKPIMultiSortMeta,
        sortOrder: -1,
      };
    }
    let searchFilter = this.searchFilter;
    if (searchFilter == null) {
      let sortOrder =
        this.modelMasterKpi.orderType.type == OrderTypesEnum.LatestOnRight
          ? [
            { field: "year", order: 1 },
            { field: "quarter", order: 1 },
          ]
          : [
            { field: "year", order: -1 },
            { field: "quarter", order: -1 },
          ];
      searchFilter = {
        sortOrder: sortOrder,
        periodType: this.modelMasterKpi.periodType.type,
      };

      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            this.modelMasterKpi.startPeriod.getFullYear(),
            this.modelMasterKpi.startPeriod.getMonth(),
            this.modelMasterKpi.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            this.modelMasterKpi.endPeriod.getFullYear(),
            this.modelMasterKpi.endPeriod.getMonth(),
            this.modelMasterKpi.endPeriod.getDate()
          )
        );
      }
    } else {
      if (searchFilter.periodType == "Date Range") {
        searchFilter.startPeriod = new Date(
          Date.UTC(
            searchFilter.startPeriod.getFullYear(),
            searchFilter.startPeriod.getMonth(),
            searchFilter.startPeriod.getDate()
          )
        );
        searchFilter.endPeriod = new Date(
          Date.UTC(
            searchFilter.endPeriod.getFullYear(),
            searchFilter.endPeriod.getMonth(),
            searchFilter.endPeriod.getDate()
          )
        );
      }
    }
    this.getFundKPIValues(searchFilter, event)
  }
  clearData() {
    this.loading = false;
    this.isLoader = false;
    this.tableColumns = [];
    this.tableResult = [];
    this.tableResultClone = [];
    this.auditLogList = [];
    this.IsPageLoad = false;
  }
  convertUnits() {
    this.tableResult = [];
    let local = this;
    let masterValueUnit = this.fundKpiValueUnit;
    this.tableResultClone.forEach(function (
      value: any
    ) {
      let valueClone = JSON.parse(JSON.stringify(value));
      if (valueClone["KPI Info"] != "%" && valueClone["KPI Info"] != "x" && valueClone["KPI Info"] != "#" &&
        valueClone["KPI Info"] != "Text" && valueClone["KPI Info"] != ""
      ) {
        switch (Number(masterValueUnit.typeId)) {
          case FinancialValueUnitsEnum.Absolute:
            break;
          case FinancialValueUnitsEnum.Thousands:
            valueClone = local.conversionValue(valueClone, local, 1000);
            break;
          case FinancialValueUnitsEnum.Millions:
            valueClone = local.conversionValue(valueClone, local, 1000000);
            break;
          case FinancialValueUnitsEnum.Billions:
            valueClone = local.conversionValue(valueClone, local, 1000000000);
            break;
        }
      }
      local.tableResult.push(valueClone)
    });
  }
  conversionValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] =
          !isNil(valueClone[col.field]) ? !isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field])))
            ? (valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field]) / value
            : valueClone[col.field] : valueClone[col.field];
      }
    });
    return valueClone;
  }
  kpiTable_GlobalFilter(event) {
    this.fundKpiValueUnit = event?.UnitType == undefined ? {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    } : event?.UnitType;
    this.searchFilter = event;
    this.kpiCurrencyFilterModel = event;
    if(this.currencyCode != null && event?.currencyCode != null && this.currencyCode != event?.currencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
    this.getFundMasterKPIValues(null);
    this.menuTrigger.closeMenu();
  }

  onChangePeriodOption(type) {
    this.filterOptions.forEach((x) => (x.key = false));
    if (type?.field == "Monthly") {
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if (type?.field == "Quarterly") {
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
    } else {
      this.isMonthly = false;
      this.isQuarterly = false;
      if (type != undefined)
        type.key = this.isAnnually = true;
    }
    this.setDefaultTypeTab();
    this.getFundMasterKPIValues(null);
  }
  setDefaultTypeTab = () => {
    if (this.isMonthly)
      this.defaultType = PeriodTypeFilterOptions.Monthly;
    else if (this.isQuarterly)
      this.defaultType = PeriodTypeFilterOptions.Quarterly;
    else
      this.defaultType = PeriodTypeFilterOptions.Annual;
  }
  selectValueTab(tab: ITab) {
    this.modelList.moduleId = tab.moduleId;
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.tabName = tab.name;
    if (tab?.name == "IC") {
      this.isLtm = false;
      this.isYtd = false;
    }
    this.setPeriodsOptions(this.subSectionFields);
    this.getFundMasterKPIValues(null);
  }
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      let tabList = x.body?.financialTypesModelList;
      let pageConfigTabs = this.subSectionFields;
      tabList = tabList?.filter((item: any) =>
        pageConfigTabs?.some((otherItem: any) =>
          otherItem.aliasName==item.name
        )
      );
      if (tabList != undefined && tabList?.length > 0) {
        tabList = tabList.map((tab: any) => ({
          ...tab,
          moduleId: this.modelList.moduleId 
        }));
        this.tabValueTypeList = tabList;
        this.tabValueTypeList[0].active = true;
        this.tabName = this.tabValueTypeList[0].name;
        this.setPeriodsOptions(pageConfigTabs);
      }
    });
  }
  private setPeriodsOptions(pageConfigTabs: any[]) {
    let ltmYTDPeriodType = this.setLtmYtdPeriodType(this.tabName);
    let periodOptions = PeriodType.filterOptions;
    let activeTabData = pageConfigTabs?.find(
      (x) => x.aliasName == ltmYTDPeriodType
    );
    if (this.isLtm || this.isYtd)
      this.valueTypeString = ltmYTDPeriodType;
    else
      this.valueTypeString = undefined;
    if (activeTabData == undefined) {
      activeTabData = this.processPageLoadView(activeTabData, ltmYTDPeriodType);
    }
    this.filterOptions = periodOptions?.filter(item =>
      activeTabData?.chartValue?.some(otherItem => otherItem === item.field))
      ?.map(item => ({ ...item }));
    let periodType = this.filterOptions.find(x => x.key);
    if (periodType == undefined && this.filterOptions?.length > 0) {
      for (const element of periodOptions) {
        element.key = false;
      }
      this.filterOptions[0].key = true;
      periodType = this.filterOptions[0];
    }
    this.onChangePeriodOption(periodType);
  }

  showErrorToast(message: string, title: string = '', position: string = ImpactKPIConstants.ToastCenterCenter): void {
    this.toastrService.error(message, title, { positionClass: position });
  }

  onChangeValueTypeOption(type) {
    if (this.tabName != FinancialsValueTypes.IC) {
      this.valueTypeString = this.tabName + " " + type;
      let isPageLoad = this.setPageLoad();
      if (isPageLoad) {
        this.isMonthly = true;
        this.isAnnually = false;
        this.isQuarterly = false;
        this.setDefaultTypeTab();
      }
      this.setLtmAndYtdFlags(type);
      if (!this.isLtm && !this.isYtd) {
        this.valueTypeString = undefined;
      }
      this.setPeriodsOptions(this.subSectionFields);
    }
  }
  setLtmAndYtdFlags(type: string) {
    if (type == FinancialsValueTypes.YTD) {
      this.isYtd = !this.isYtd;
      this.isLtm = false;
    } else {
      this.isLtm = !this.isLtm;
      this.isYtd = false;
    }
  }
  setPageLoad() {
    let isPagLoad = false;
    if (this.isLtm && this.isLtmPageLoad) {
      isPagLoad = true;
      this.isLtmPageLoad = false;
    } else if (this.isYtd && this.isYtdPageLoad) {
      isPagLoad = true;
      this.isYtdPageLoad = false;
    }
    return isPagLoad;
  }
  shouldAddLtm(periodType: string) {
    return (
      this.isLtm &&
      this.hasLtm &&
      !periodType.includes(FinancialsValueTypes.LTM)
    );
  }
  shouldAddYtd(periodType: string) {
    return (
      this.isYtd &&
      this.hasYtd &&
      !periodType.includes(FinancialsValueTypes.YTD)
    );
  }
  resetPeriodFlags() {
    this.isLtm = false;
    this.isYtd = false;
  }
  getPeriodTypeWithSuffix(periodType: string) {
    if (this.shouldAddLtm(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.LTM}`;
    } else if (this.shouldAddYtd(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.YTD}`;
    } else {
      this.resetPeriodFlags();
    }
    return periodType;
  }
  setLtmYtdPeriodType(periodType: string) {
    const ltmType = `${periodType} ${FinancialsValueTypes.LTM}`;
    const ytdType = `${periodType} ${FinancialsValueTypes.YTD}`;
    this.hasLtm = this.checkExistence(ltmType);
    this.hasYtd = this.checkExistence(ytdType);
    if (this.hasLtm || this.hasYtd)
      periodType = this.getPeriodTypeWithSuffix(periodType);
    return periodType;
  }
  checkExistence(type: string) {
    let res = this.subSectionFields?.filter((x: any) => x.aliasName === type);
    return res?.length > 0;
  }
  updatePeriodTypeFlags() {
    if (this.hasYtd && !this.isYtd && (!this.hasLtm || !this.isLtm)) {
      this.isYtd = true;
      this.isLtm = false;
    } else if (this.hasLtm && !this.isLtm && (!this.hasYtd || !this.isYtd)) {
      this.isLtm = true;
      this.isYtd = false;
    }
  }
  processPageLoadView(
    selectedPeriodTypeConfiguration: any,
    periodType: string
  ) {
    if (selectedPeriodTypeConfiguration == undefined) {
      this.updatePeriodTypeFlags();
      periodType = this.getPeriodTypeWithSuffix(periodType);
      selectedPeriodTypeConfiguration =
        this.findPeriodTypeConfiguration(periodType);
    }
    this.valueTypeString = periodType;
    return selectedPeriodTypeConfiguration;
  }
  findPeriodTypeConfiguration(periodType: string) {
    return this.pageConfigResponse?.kpiConfigurationData.find(
      (x) => x.aliasName === periodType
    );
  }

  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == ImpactKPIConstants.ok) {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
      this.isValueUpdated = !this.isValueUpdated;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getFundMasterKPIValues(null);
  }

  showSuccessToast(
    message: string,
    title: string = "",
    position: string = ImpactKPIConstants.ToastCenterCenter
  ): void {
    this.toastrService.success(message, title, { positionClass: position });
  }

  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }

}