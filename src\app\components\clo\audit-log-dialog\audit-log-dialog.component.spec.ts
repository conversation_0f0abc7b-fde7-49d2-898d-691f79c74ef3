import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AuditLogDialogComponent } from './audit-log-dialog.component';
import { AuditLogService } from '../services/audit-log.service';
import { AuditLogModel } from '../models/audit-log.model';

interface AuditLogEntry extends AuditLogModel {
  showComments?: boolean;
}
import { of, throwError } from 'rxjs';

describe('AuditLogDialogComponent', () => {
  let component: AuditLogDialogComponent;
  let fixture: ComponentFixture<AuditLogDialogComponent>;
  let auditLogService: jasmine.SpyObj<AuditLogService>;
  let toastrService: ToastrService;
  let activeModal: NgbActiveModal;

  beforeEach(async () => {
    const auditLogServiceSpy = jasmine.createSpyObj('AuditLogService', ['downloadDocument']);

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        ToastrModule.forRoot(),
        BrowserAnimationsModule,
        AuditLogDialogComponent // Import the standalone component
      ],
      providers: [
        { provide: AuditLogService, useValue: auditLogServiceSpy },
        ToastrService,
        NgbActiveModal
      ]
    }).compileComponents();

    auditLogService = TestBed.inject(AuditLogService) as jasmine.SpyObj<AuditLogService>;
    toastrService = TestBed.inject(ToastrService);
    activeModal = TestBed.inject(NgbActiveModal);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AuditLogDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should transform data correctly on initialization', () => {
    const mockData: AuditLogModel[] = [
      {
        auditId: 1,
        companyId: 'companyId1',
        isDeleted: false,
        documentId: 'documentId1',
        newValue: 'newValue1',
        oldValue: 'oldValue1',
        auditType: 0,
        supportingDocumentName: 'file1',
        supportingDocumentId: '1',
        createdBy: 1,
        userName: 'User One',
        modifiedOn: new Date(),
        comment: 'comment1',
        createdOn: new Date(),
        modifiedBy: 1,
        rowIdentity: 'row1',
        columnIdentity: 'column1',
        tableIdentifier: 'table1'
      }
    ];
    component.gridData = mockData;
    component.ngOnInit();
    expect(component.displayData.length).toBe(1);
    expect(component.displayData[0].currentValue).toBe('newValue1');
    expect(component.displayData[0].source).toBe('File Upload');
  });

  it('should close the modal', () => {
    spyOn(activeModal, 'close');
    component.closeModal();
    expect(activeModal.close).toHaveBeenCalled();
  });

  it('should download file successfully', () => {
    const mockAuditLogEntry = {
      currentValue: 'newValue1',
      oldValue: 'oldValue1',
      source: 'File Upload',
      sourceFile: 'file1',
      sourceFileId: '1',
      supportingEvidenceId: '',
      supportingEvidence: '',
      createdBy: 'User ID: user1',
      userName: 'User One',
      userNameSymbol: 'UO',
      dateTime: new Date().toLocaleString(),
      comment: 'comment1'
    };
    const mockBlob = new Blob([''], { type: 'application/octet-stream' });
    auditLogService.downloadDocument.and.returnValue(of(mockBlob));
    spyOn(window.URL, 'createObjectURL').and.returnValue('blob:url');
    spyOn(document, 'createElement').and.returnValue({
      href: '',
      download: '',
      click: jasmine.createSpy('click')
    } as any);

    component.downloadFile(mockAuditLogEntry, false);
    expect(auditLogService.downloadDocument).toHaveBeenCalledWith('1');
  });

  it('should show error when file download fails', () => {
    const mockAuditLogEntry = {
      currentValue: 'newValue1',
      oldValue: 'oldValue1',
      source: 'File Upload',
      sourceFile: 'file1',
      sourceFileId: '1',
      supportingEvidenceId: '',
      supportingEvidence: '',
      createdBy: 'User ID: user1',
      userName: 'User One',
      userNameSymbol: 'UO',
      dateTime: new Date().toLocaleString(),
      comment: 'comment1'
    };
    auditLogService.downloadDocument.and.returnValue(throwError({ error: 'Error' }));
    spyOn(toastrService, 'error');

    component.downloadFile(mockAuditLogEntry, false);
    expect(toastrService.error).toHaveBeenCalledWith('Error downloading file', '', { positionClass: 'toast-center-center' });
  });

  it('should open comments', () => {
    const mockDataItem = { comment: 'Test comment' };
    spyOn(console, 'log');
    component.openComments(mockDataItem);
    expect(console.log).toHaveBeenCalledWith('Opening comments for:', mockDataItem);
  });

  it('should toggle comments', () => {
    const mockDataItem = {
      currentValue: '',
      oldValue: '',
      source: '',
      sourceFile: '',
      sourceFileId: '',
      supportingEvidenceId: '',
      supportingEvidence: '',
      createdBy: '',
      userName: '',
      userNameSymbol: '',
      dateTime: '',
      comment: '',
      showComments: false
    };
    const mockEvent = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { closest: jasmine.createSpy('closest').and.returnValue({ getBoundingClientRect: jasmine.createSpy('getBoundingClientRect').and.returnValue({ left: 0, bottom: 0 }) }) } };
    component.displayData = [{
      currentValue: '',
      oldValue: '',
      source: '',
      sourceFile: '',
      sourceFileId: '',
      supportingEvidenceId: '',
      supportingEvidence: '',
      createdBy: '',
      userName: '',
      userNameSymbol: '',
      dateTime: '',
      comment: '',
      showComments: true
    }, mockDataItem];

    component.toggleComments(mockDataItem, mockEvent);
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    expect((component.displayData[0] as unknown as AuditLogEntry).showComments).toBeFalse();
    expect(mockDataItem.showComments).toBeTrue();
  });
});