import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CloCommentriesComponent } from './clo-commentries.component';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { InjectionToken } from '@angular/core';

// Define the BASE_URL token
export const BASE_URL = new InjectionToken<string>('BASE_URL');

describe('CloCommentriesComponent', () => {
  let component: CloCommentriesComponent;
  let fixture: ComponentFixture<CloCommentriesComponent>;
  let investCompanyService: InvestCompanyService;
  let toastrService: ToastrService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CloCommentriesComponent],
      imports: [HttpClientTestingModule], // Import HttpClientTestingModule
      providers: [
        InvestCompanyService,
         { provide: 'BASE_URL', useValue: 'http://localhost/' } ,// <-- Add this
        { provide: ToastrService, useValue: { success: jasmine.createSpy(), error: jasmine.createSpy(), warning: jasmine.createSpy() } }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CloCommentriesComponent);
    component = fixture.componentInstance;
    investCompanyService = TestBed.inject(InvestCompanyService);
    toastrService = TestBed.inject(ToastrService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle save action', () => {
    spyOn(component.save, 'emit');
    const clo = { isEdit: true };
    component.onSave(clo);
    expect(component.save.emit).toHaveBeenCalledWith(clo);
  });

  it('should handle cancel action', () => {
    spyOn(component.cancel, 'emit');
    const clo = { isEdit: true };
    component.onCancel(clo);
    expect(component.cancel.emit).toHaveBeenCalledWith(clo);
  });

  it('should handle reset action', () => {
    spyOn(component.reset, 'emit');
    const clo = { newComment: 'Test comment' };
    component.onReset(clo);
  });
});