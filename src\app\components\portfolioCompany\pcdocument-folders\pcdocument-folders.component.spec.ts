import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { PcdocumentFoldersComponent } from './pcdocument-folders.component';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { of, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('PcdocumentFoldersComponent', () => {
  let component: PcdocumentFoldersComponent;
  let fixture: ComponentFixture<PcdocumentFoldersComponent>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;

  beforeEach(async () => {
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);
    repositoryConfigServiceSpy = jasmine.createSpyObj('RepositoryConfigService', ['getRepositoryStructureData']);

    await TestBed.configureTestingModule({
      declarations: [PcdocumentFoldersComponent],
      providers: [
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: RepositoryConfigService, useValue: repositoryConfigServiceSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PcdocumentFoldersComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch tree data on ngOnInit if PortfolioCompanyId is set', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(of({ isSuccess: true, data: [{ name: 'Folder1', isExpanded: false }] }));
    component.ngOnInit();
    tick();
    expect(component.isLoading).toBeFalse();
    expect(component.treeData.length).toBe(1);
    expect(component.filteredTreeData.length).toBe(1);
  }));

  it('should show error if getRepositoryStructureData fails', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(of({ isSuccess: false, message: 'Error' }));
    component.ngOnInit();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Error');
  }));

  it('should show error on getRepositoryStructureData error', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(throwError(() => ({})));
    component.ngOnInit();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Error while fetching data');
  }));

  it('should filter folders by search term', () => {
    component.treeData = [
      { name: 'Alpha', isExpanded: false },
      { name: 'Beta', isExpanded: false }
    ];
    component.searchTerm = 'alp';
    component.filterFolders();
    expect(component.filteredTreeData.length).toBe(1);
    expect(component.filteredTreeData[0].name).toBe('Alpha');
  });

  it('should reset filteredTreeData if searchTerm is empty', () => {
    component.treeData = [
      { name: 'Alpha', isExpanded: false },
      { name: 'Beta', isExpanded: false }
    ];
    component.searchTerm = '';
    component.filterFolders();
    expect(component.filteredTreeData.length).toBe(2);
  });

  it('should emit folderSelected with correct path and isFromUnconfig', () => {
    spyOn(component.folderSelected, 'emit');
    const node = { name: 'Un-Configured/Folder', isExpanded: false, path: 'Un-Configured/Folder' };
    component.unconfigured = 'Un-Configured';
    component.toggleFolder(node as any);
    expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 'Folder', isFromUnconfig: true });
  });

  it('should emit folderSelected with isFromUnconfig false for normal folder', () => {
    spyOn(component.folderSelected, 'emit');
    const node = { name: 'Folder', isExpanded: false, path: 'Folder' };
    component.toggleFolder(node as any);
    expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 'Folder', isFromUnconfig: false });
  });
});
