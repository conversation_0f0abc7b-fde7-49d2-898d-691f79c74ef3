import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule, FormsModule, FormGroup, FormControl } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { DropDownsModule, ComboBoxModule } from '@progress/kendo-angular-dropdowns';
import { TextBoxModule } from '@progress/kendo-angular-inputs';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { of, throwError } from 'rxjs';
import { AddCloComponent } from './add-clo-list.component';
import { CloService } from '../../../services/clo.service';
import { CloListService } from '../clo-list/clo-list.service';
import { AddClo } from '../add-clo-list.model';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

describe('AddCloComponent', () => {
    let component: AddCloComponent;
    let fixture: ComponentFixture<AddCloComponent>;
    let mockCloService: jasmine.SpyObj<CloService>;
    let mockCloListService: jasmine.SpyObj<CloListService>;
    let mockToastrService: jasmine.SpyObj<ToastrService>;
    let mockRouter: jasmine.SpyObj<Router>;

    const mockCloData = {
        clO_ID: 123,
        uniqueID: 'test-id',
        companyId: 1,
        companyName: 'Test Company',
        domicile: 'US',
        issuer: 'Test Issuer',
        arranger: 'Test Arranger',
        trustee: 'Test Trustee',
        priced: '1 January 2023',
        closed: '15 January 2023',
        lastRefiDate: '20 February 2023',
        lastResetDate: '1 March 2023',
        callEndDate: '1 April 2023',
        originalEndOfReinvestmentDate: '1 May 2023',
        currentEndOfReinvestmentDate: '1 June 2023',
        currentMaturityDate: '31 December 2023'
    };

    beforeEach(async () => {
        mockCloService = jasmine.createSpyObj('CloService', ['getCloById']);
        mockCloService.getCloById.and.returnValue(of(mockCloData));

        mockCloListService = jasmine.createSpyObj('CloListService', ['saveClo']);
        mockCloListService.saveClo.and.returnValue(of({ status: true, message: 'Success' }));

        mockRouter = jasmine.createSpyObj('Router', ['navigate']);
        mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);

        await TestBed.configureTestingModule({
            declarations: [AddCloComponent],
            imports: [
                RouterTestingModule,
                ReactiveFormsModule,
                FormsModule,
                HttpClientTestingModule,
                BrowserAnimationsModule,
                ToastrModule.forRoot(),
                DropDownsModule,
                TextBoxModule,
                ComboBoxModule,
                DateInputsModule
            ],
            providers: [
                DatePipe,
                { provide: CloService, useValue: mockCloService },
                { provide: CloListService, useValue: mockCloListService },
                { provide: ToastrService, useValue: mockToastrService },
                { provide: Router, useValue: mockRouter },
                { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
                { provide: BreadcrumbService, useValue: { setBreadcrumbs: jasmine.createSpy() } },
                {
                    provide: ActivatedRoute,
                    useValue: {
                        queryParams: of({ 
                            name: 'Test Company', 
                            id: '1', 
                            uniqueId: '' // Set to empty for default tests
                        })
                    }
                }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(AddCloComponent);
        component = fixture.componentInstance;

        // Skip the actual ngOnInit to have more control over test setup
        spyOn(component, 'ngOnInit').and.callFake(() => {});

        component.cloForm = new FormGroup({
            companyName: new FormControl('Test Company'),
            domicile: new FormControl(null),
            issuer: new FormControl(''),
            arranger: new FormControl(''),
            trustee: new FormControl(''),
            priced: new FormControl(''),
            closed: new FormControl(''),
            lastRefiDate: new FormControl(''),
            lastResetDate: new FormControl(''),
            callEndDate: new FormControl(''),
            originalEndOfReinvestmentDate: new FormControl(''),
            currentEndOfReinvestmentDate: new FormControl(''),
            currentMaturityDate: new FormControl('')
        });

        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should set current date for a control if it is empty', () => {
        const controlName = 'priced';
        component.setCurrentDate(controlName);
        const control = component.cloForm.get(controlName);
        expect(control.value).toEqual(jasmine.any(Date));
    });

    it('should trim form control values on setValue', () => {
        component.cloForm.get('issuer').setValue('  Test Issuer  ');
        component.setValue('issuer');
        expect(component.cloForm.get('issuer').value).toBe('Test Issuer');
    });

    it('should set title and isUpdate correctly for add functionality', () => {
        component.uniqueId = '';
        component.getCompanyName(); // Call this method manually
        expect(component.title).toBe('Add Collateral Loan Obligation');
        expect(component.isUpdate).toBeFalse();
    });
    
    it('should set title and isUpdate correctly for update functionality', () => {
        // Create a new TestBed specifically for this test with updated route params
        TestBed.resetTestingModule();
        
        TestBed.configureTestingModule({
            declarations: [AddCloComponent],
            imports: [
                RouterTestingModule,
                ReactiveFormsModule,
                FormsModule,
                HttpClientTestingModule,
                BrowserAnimationsModule,
                ToastrModule.forRoot(),
                DropDownsModule,
                TextBoxModule,
                ComboBoxModule,
                DateInputsModule
            ],
            providers: [
                DatePipe,
                { provide: CloService, useValue: mockCloService },
                { provide: CloListService, useValue: mockCloListService },
                { provide: ToastrService, useValue: mockToastrService },
                { provide: Router, useValue: mockRouter },
                { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
                { provide: BreadcrumbService, useValue: { setBreadcrumbs: jasmine.createSpy() } },
                {
                    provide: ActivatedRoute,
                    useValue: {
                        queryParams: of({ 
                            name: 'Test Company', 
                            id: '1', 
                            uniqueId: 'test-id' // Set uniqueId for update mode
                        })
                    }
                }
            ]
        }).compileComponents();
        
        // Create new component with update route parameters
        const updateFixture = TestBed.createComponent(AddCloComponent);
        const updateComponent = updateFixture.componentInstance;
        
        // Skip ngOnInit but call getCompanyName directly
        spyOn(updateComponent, 'ngOnInit').and.callFake(() => {});
        updateComponent.cloForm = new FormGroup({
            companyName: new FormControl('Test Company'),
            domicile: new FormControl(null),
            issuer: new FormControl(''),
            // Other controls...
        });
        
        // Call getCompanyName which will read from route params
        updateComponent.getCompanyName();
        
        // Verify update mode is correctly set
        expect(updateComponent.title).toBe('Update Collateral Loan Obligation');
        expect(updateComponent.isUpdate).toBeTrue();
    });
  
    it('should fetch CLO data successfully', () => {
        mockCloService.getCloById.and.returnValue(of(mockCloData));
        spyOn(component, 'handleCloData');
        
        component.uniqueId = 'test-id';
        component.fetchCloData();
        
        expect(mockCloService.getCloById).toHaveBeenCalledWith('test-id');
        expect(component.cLO_Id).toBe(123);
    });
    
    it('should handle error when fetching CLO data', () => {
        mockCloService.getCloById.and.returnValue(throwError(() => new Error('Test error')));
        spyOn(console, 'error');
        
        component.uniqueId = 'test-id';
        component.fetchCloData();
        
        expect(console.error).toHaveBeenCalled();
        expect(component.isLoading).toBeFalse();
    });
    
    it('should properly map form values to model', () => {
        const testDate = new Date(2023, 0, 1); // January 1, 2023
        
        component.companyId = 1;
        component.companyName = 'Test Company';
        component.uniqueId = 'test-id';
        component.cLO_Id = 123;
        
        component.cloForm.patchValue({
            domicile: 'US',
            issuer: 'Test Issuer',
            priced: testDate
        });
        
        const result = component.mapFormToModel();
        
        expect(result.companyId).toBe(1);
        expect(result.companyName).toBe('Test Company');
        expect(result.uniqueID).toBe('test-id');
        expect(result.cLO_Id).toBe(123);
        expect(result.domicile).toBe('US');
        expect(result.issuer).toBe('Test Issuer');
        // The date formatting depends on DatePipe, so check it's not empty
        expect(result.priced).not.toBe('');
    });
    
    it('should save CLO data successfully', () => {
        component.savedData = new AddClo();
        mockCloListService.saveClo.and.returnValue(of({ status: true, message: 'Success' }));
        
        component.saveCLO();
        
        expect(mockCloListService.saveClo).toHaveBeenCalled();
        expect(mockToastrService.success).toHaveBeenCalled();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/clo-list']);
    });
    
    it('should handle error when saving CLO data', () => {
        component.savedData = new AddClo();
        mockCloListService.saveClo.and.returnValue(throwError(() => new Error('Test error')));
        
        component.saveCLO();
        
        expect(mockToastrService.error).toHaveBeenCalled();
        expect(component.isLoading).toBeFalse();
    });
    
    it('should navigate to CLO list on cancel', () => {
        component.onCancel();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/clo-list']);
    });
    
    it('should reset form correctly', () => {
        component.cloForm.patchValue({
            issuer: 'Test value',
            domicile: 'US'
        });
        component.isEdited = true;
        
        component.onReset();
        
        expect(component.cloForm.get('issuer').value).toBeFalsy();
        expect(component.isEdited).toBeFalse();
    });
});