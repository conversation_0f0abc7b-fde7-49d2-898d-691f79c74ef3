import { ComponentFixture, TestBed } from "@angular/core/testing";
import { InvestorDashboardComponent } from "./investorDashboard.component";
import { InvestorService } from "../../../services/investor.service";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { NO_ERRORS_SCHEMA } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { MiscellaneousService } from "../../../services/miscellaneous.service";
import { ActivatedRoute, convertToParamMap } from "@angular/router";
import { of, throwError } from "rxjs";
import { InvestorDashBoardStatic } from "src/app/common/constants";

describe("InvestorDashboardComponent", () => {
  let component: InvestorDashboardComponent;
  let fixture: ComponentFixture<InvestorDashboardComponent>;
  beforeEach(async () => {
    const activatedRouteStub = () => ({ snapshot: { queryParams: {} } });
    const miscellaneousServiceStub = () => ({
      bindYearList: () => of([{ name: "2022", value: 2022 }]),
    });
    const investorStub = () => ({
      getInvestorDashBoard: (inputData1: any) => of([{}]),
    });
    await TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [InvestorDashboardComponent],
      providers: [
        { provide: "BASE_URL", useValue: "http://localhost" },
        { provide: InvestorService, useFactory: investorStub },
        {
          provide: ActivatedRoute,
          useFactory: activatedRouteStub,
          useValue: { snapshot: { params: convertToParamMap({ id: "1" }) } },
        },
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(InvestorDashboardComponent);
    component = fixture.componentInstance;
    component.model = { FromDate: "2022-01-01", ToDate: "2022-12-31" };
    component.id = "encryptedId";
    component.model = {};
    component.dashBoardConstant = InvestorDashBoardStatic;
  });

  it("should be created ", () => {
    expect(component).toBeTruthy();
  });
  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`Unit has default value`, () => {
    expect(component.Unit).toEqual(`M`);
  });

  it(`Currency has default value`, () => {
    expect(component.Currency).toEqual(`USD`);
  });

  it(`sectorData has default value`, () => {
    expect(component.sectorData).toEqual([]);
  });

  it(`headerData has default value`, () => {
    expect(component.headerData).toEqual([]);
  });

  it(`totalFunds has default value`, () => {
    expect(component.totalFunds).toEqual([]);
  });

  it(`totalPortfolioCompanies has default value`, () => {
    expect(component.totalPortfolioCompanies).toEqual([]);
  });

  it(`width has default value`, () => {
    expect(component.width).toEqual(0);
  });

  it(`isData has default value`, () => {
    expect(component.isData).toEqual(true);
  });

  it(`pageConfigField has default value`, () => {
    expect(component.pageConfigField).toEqual([]);
  });

  it(`pageConfigFieldClone has default value`, () => {
    expect(component.pageConfigFieldClone).toEqual([]);
  });

  it(`topCompanyData has default value`, () => {
    expect(component.topCompanyData).toEqual([]);
  });

  it(`TVPI has default value`, () => {
    expect(component.TVPI).toEqual(`0.0`);
  });

  it(`DPI has default value`, () => {
    expect(component.DPI).toEqual(`0.0`);
  });

  it(`RVPI has default value`, () => {
    expect(component.RVPI).toEqual(`0.0`);
  });
  it("#onResized should set width to event.newRect.width", () => {
    const event = { newRect: { width: 500 } };
    component.onResized(event);
    expect(component.width).toBe(500);
  });

  it("#onResizedDashboard should set width and dashboardContainer correctly", () => {
    const event = { newRect: { width: 500 } };
    const miscService = TestBed.inject(MiscellaneousService);
    component.onResizedDashboard(event);
    expect(component.width).toBe(500);
    expect(component.dashboardContainer).toBe(
      "investor-dashboard-container-with-expand"
    );
  });

  it("#getInvestorDashboardDetails should set properties correctly on success", () => {
    const result = {
      pageFieldValueModel: [
        { name: "TotalFunds", value: 100 },
        { name: "PortfolioCompanies", value: 10 },
      ],
      expandoObject: { TVPIbyVintageYearofInvestor: [], sectorwise: [] },
      topCompaniesTotalValue: [{ companyName: "Company1", totalValue: 1000 }],
    };
    const mockInvestorService = TestBed.inject(InvestorService);
    spyOn(mockInvestorService, "getInvestorDashBoard").and.returnValue(
      of(result)
    );

    component.getInvestorDashboardDetails("val", "event", "dateType");

    expect(component.pageConfigField).toEqual(result.pageFieldValueModel);
    expect(component.totalFunds).toEqual([{ name: "TotalFunds", value: 100 }]);
    expect(component.totalPortfolioCompanies).toEqual([
      { name: "PortfolioCompanies", value: 10 },
    ]);
    expect(component.enableview).toBeTrue();
    expect(component.dashboarddata).toEqual(result.expandoObject);
    expect(component.TVPIByVintageYear).toEqual([]);
    expect(component.sectorData).toEqual([]);
    expect(component.topCompanyData).toEqual([
      { "Company Name": "Company1", "Total Value": 1000 },
    ]);
    expect(component.isData).toBeFalse();
  });

  it("#getInvestorDashboardDetails should set isData to false on error", () => {
    const mockInvestorService = TestBed.inject(InvestorService);
    spyOn(mockInvestorService, "getInvestorDashBoard").and.returnValue(
      throwError(() => "error")
    );

    component.getInvestorDashboardDetails("val", "event", "dateType");

    expect(component.isData).toBeFalse();
  });

  it("#loadModelDefault should set FromDate and ToDate correctly when val is true and date is FromDate", () => {
    const event = { item: 2022 };
    component.ToDate = 2021;
    component.loadModelDefault(true, event, "FromDate");
    expect(component.FromDate).toBe(event);
    expect(component.ToDate).toBe(2021);
  });

  it("#loadModelDefault should set FromDate and ToDate correctly when val is true and date is not FromDate", () => {
    const event = { item: 2022 };
    component.FromDate = 2023;
    component.loadModelDefault(true, event, "ToDate");
    expect(component.ToDate).toBe(event);
    expect(component.FromDate).toBe(2023);
  });
});
