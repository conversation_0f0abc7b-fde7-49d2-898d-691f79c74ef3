import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { KpiConfigurationComponent } from './kpi-configuration.component';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { of } from 'rxjs';

describe('KpiConfigurationComponent', () => {
  let component: KpiConfigurationComponent;
  let fixture: ComponentFixture<KpiConfigurationComponent>;
  let panelbarItemService: jasmine.SpyObj<PanelbarItemService>;

  const mockCompanyList = [
    { id: 1, name: 'Company 1' },
    { id: 2, name: 'Company 2' }
  ];

  const mockCLOTableList = [
    { id: 1, name: 'Table 1' },
    { id: 2, name: 'Table 2' }
  ];

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PanelbarItemService', [
      'getInvestCompanyList',
      'getCLOTableDetails',
      'setCompany'
    ]);
    spy.getInvestCompanyList.and.returnValue(of(mockCompanyList));
    spy.getCLOTableDetails.and.returnValue(of(mockCLOTableList));

    await TestBed.configureTestingModule({
      imports: [],
      providers: [
        { provide: PanelbarItemService, useValue: spy }
      ]
    }).compileComponents();

    panelbarItemService = TestBed.inject(PanelbarItemService) as jasmine.SpyObj<PanelbarItemService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(KpiConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.selectedCategory).toBe('');
    expect(component.selectedSubCategory).toBe('');
    expect(component.selectedCompanyItem).toBeNull();
    expect(component.selectedTableItem).toBeNull();
  });

  it('should load company list on init', fakeAsync(() => {
    component.ngOnInit();
    tick();

    expect(panelbarItemService.getInvestCompanyList).toHaveBeenCalled();
    expect(component.compnayList).toEqual(mockCompanyList);
  }));

  it('should load CLO table details on init', fakeAsync(() => {
    component.ngOnInit();
    tick();

    expect(panelbarItemService.getCLOTableDetails).toHaveBeenCalled();
    expect(component.CloTableList).toEqual(mockCLOTableList);
  }));

  it('should handle empty company list response', fakeAsync(() => {
    panelbarItemService.getInvestCompanyList.and.returnValue(of([]));
    component.getInvestCompanies();
    tick();

    expect(component.compnayList).toBeUndefined();
  }));

  it('should handle empty CLO table list response', fakeAsync(() => {
    panelbarItemService.getCLOTableDetails.and.returnValue(of([]));
    component.getCLOTableDetails();
    tick();

    expect(component.CloTableList).toBeUndefined();
  }));

  it('should update selectedCategory on category change', () => {
    const mockEvent = { target: { value: 'Category 1' } };
    component.onCategoryChange(mockEvent);
    expect(component.selectedCategory).toBe('Category 1');
  });

  it('should update selectedSubCategory on sub-category change', () => {
    const mockEvent = { target: { value: 'SubCategory 1' } };
    component.onSubCategoryChange(mockEvent);
    expect(component.selectedSubCategory).toBe('SubCategory 1');
  });

  it('should have predefined categories', () => {
    expect(component.categories).toBeDefined();
    expect(component.categories.length).toBe(2);
    expect(component.categories[0].name).toBe('Option 1');
  });

  it('should have predefined sub-categories', () => {
    expect(component.subCategories).toBeDefined();
    expect(component.subCategories.length).toBe(2);
    expect(component.subCategories[0].name).toBe('Option 1');
  });
});
