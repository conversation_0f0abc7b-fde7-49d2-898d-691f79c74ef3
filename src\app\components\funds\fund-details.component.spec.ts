import { ComponentFixture, TestBed, fakeAsync, tick } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA, ChangeDetectorRef } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { LazyLoadEvent } from "primeng/api";
import { AccountService } from "../../services/account.service";
import { DealService } from "../../services/deal.service";
import { FundService } from "../../services/funds.service";
import {
  MiscellaneousService,
  FinancialValueUnitsEnum,
} from "../../services/miscellaneous.service";
import { ReportService, ReportType } from "../../services/report.service";
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { FundReportService } from "src/app/services/fund-report.service";
import { AppSettingService } from "../../services/appsettings.service";
import { ToastrService } from "ngx-toastr";
import { FormsModule } from "@angular/forms";
import { FundDetailsComponent } from "./fund-details.component";
import { MatMenuModule } from "@angular/material/menu";
import { of, throwError } from "rxjs"; // Import the missing 'of' and 'throwError' symbols
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { ErrorMessage } from "../../services/miscellaneous.service";
// Add these new imports
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { LpTemplateModel, PdfDownloadModel } from "../lp-report-config/models/lp-report-config.model";

// Add this constant at the top of the file
const mockBaseURL = 'http://test-api.com';

describe('FundDetailsComponent', () => {
  let component: FundDetailsComponent;
  let fixture: ComponentFixture<FundDetailsComponent>;
  let router: Router;
  let fundService: FundService;
  let dealService: DealService;
  let miscService: MiscellaneousService;
  let portfolioCompanyService: PortfolioCompanyService;
  let toastrService: ToastrService;

  const mockFundResponse = {
    code: "OK",
    body: {
      fundDetails: {
        fundList: [{
          fundID: 1,
          fundName: "Test Fund",
          encryptedFundId: "xyz123"
        }]
      },
      fundStaticDataTitle: {},
      fundStaticConfiguartionData: [],
      fundTermsConfiguartionData: [],
      fundLocationConfiguartionData: [],
      fundInvestorData: []
    }
  };

  // Add mock deals response
  const mockDealsResponse = {
    status: "OK",
    data: {
      total: 2,
      deals: [
        {
          dealID: 1,
          portfolioCompanyDetails: { companyName: "Company 1" },
          dealCustomID: "DEAL001"
        },
        {
          dealID: 2,
          portfolioCompanyDetails: { companyName: "Company 2" },
          dealCustomID: "DEAL002"
        }
      ]
    }
  };

  const mockReportResponse = {
    body: [
      {
        ReportType: ReportType.QuarterlyTVPI_IRR_FundDetails,
        Results: [
          { ValuationDate: '2023-01-01' }
        ]
      },
      {
        ReportType: ReportType.SectorwiseValues_FundDetails,
        Results: [
          { 
            'Investment Cost': 1000,
            'Total Value': 2000,
            AsofDate: '2023-01-01'
          }
        ]
      }
    ]
  };

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const fundServiceSpy = jasmine.createSpyObj('FundService', ['getFundById', 'getFundTrackRecordList']);
    const dealServiceSpy = jasmine.createSpyObj('DealService', ['getDealsList', 'getPortfolioCompanyFundHolding']);
    
    // Add mock implementation for getDealsList
    dealServiceSpy.getDealsList.and.returnValue(of(mockDealsResponse));
    // Add mock implementation for getPortfolioCompanyFundHolding
    dealServiceSpy.getPortfolioCompanyFundHolding.and.returnValue(of([]));

    const miscServiceSpy = jasmine.createSpyObj('MiscellaneousService', [
      'getMessageTimeSpan',
      'getSmallPagerLength',
      'bindYearList',
      'downloadPDFFile',
      'showAlertMessages',
      'getTitle',
      'GetPriviousPageUrl'  // Add this method to the spy
    ]);

    // Add return value for GetPriviousPageUrl
    miscServiceSpy.GetPriviousPageUrl.and.returnValue('/previous-page');

    const portfolioServiceSpy = jasmine.createSpyObj('PortfolioCompanyService', [
      'pdfExport', 
      'getPortfolioMappedLpReportTemplates',
      'lpReportPdfExport'
    ]);

    // Mock implementation for getPortfolioMappedLpReportTemplates
    portfolioServiceSpy.getPortfolioMappedLpReportTemplates.and.returnValue(of([]));

    // Mock implementation for pdfExport
    portfolioServiceSpy.pdfExport.and.returnValue(of(new Blob()));

    // Mock implementation for lpReportPdfExport  
    portfolioServiceSpy.lpReportPdfExport.and.returnValue(of(new Blob()));

    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['error']);
    const reportServiceSpy = jasmine.createSpyObj('ReportService', ['getReportData']);
    reportServiceSpy.getReportData.and.returnValue(of(mockReportResponse));
    reportServiceSpy.ReportTypeList = [
      { value: 1, label: 'Report 1' },
      { value: 2, label: 'Report 2' }
    ];
    const fundReportServiceSpy = jasmine.createSpyObj('FundReportService', ['fundReportGraphs']); // Add this
    const appSettingServiceSpy = jasmine.createSpyObj('AppSettingService', ['getConfig']);

    appSettingServiceSpy.getConfig.and.returnValue({
      DefaultNumberSystem: 1000
      // Add other required config properties
    });

    reportServiceSpy.ReportTypeList = [
      { value: 1, label: 'Report 1' },
      { value: 2, label: 'Report 2' }
    ];

    await TestBed.configureTestingModule({
      imports: [FormsModule, MatMenuModule, HttpClientTestingModule], // Add HttpClientTestingModule
      declarations: [FundDetailsComponent],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: FundService, useValue: fundServiceSpy },
        { provide: DealService, useValue: dealServiceSpy },
        { provide: MiscellaneousService, useValue: miscServiceSpy },
        { provide: PortfolioCompanyService, useValue: portfolioServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ReportService, useValue: reportServiceSpy },
        { provide: FundReportService, useValue: fundReportServiceSpy }, // Add this provider
        { provide: AppSettingService, useValue: appSettingServiceSpy },
        { provide: 'BASE_URL', useValue: mockBaseURL }, // Add this provider
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              params: { id: '123' }
            }
          }
        },
        CommonSubFeaturePermissionService
        // ...other providers
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(FundDetailsComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    fundService = TestBed.inject(FundService);
    dealService = TestBed.inject(DealService);
    miscService = TestBed.inject(MiscellaneousService);
    portfolioCompanyService = TestBed.inject(PortfolioCompanyService);
    toastrService = TestBed.inject(ToastrService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.loading).toBeFalse();
    expect(component.deals).toEqual([]);
    expect(component.fundTrackRecords).toEqual([]);
    expect(component.pageSize).toBe(100);
  });

  it('should load fund details on init', fakeAsync(() => {
    (fundService.getFundById as jasmine.Spy).and.returnValue(of(mockFundResponse));
    (miscService.GetPriviousPageUrl as jasmine.Spy).and.returnValue('/previous-page'); // Add this line
    (portfolioCompanyService.getPortfolioMappedLpReportTemplates as jasmine.Spy).and.returnValue(of([]));
    (dealService.getDealsList as jasmine.Spy).and.returnValue(of(mockDealsResponse));
    (dealService.getPortfolioCompanyFundHolding as jasmine.Spy).and.returnValue(of([]));
    
    component.ngOnInit();
    tick();

    expect(component.model).toBeTruthy();
    expect(component.model.fundName).toBe('Test Fund');
    expect(fundService.getFundById).toHaveBeenCalled();
    expect(miscService.GetPriviousPageUrl).toHaveBeenCalled(); // Add this expectation
    expect(portfolioCompanyService.getPortfolioMappedLpReportTemplates).toHaveBeenCalled();
    expect(dealService.getDealsList).toHaveBeenCalled();
  }));

  it('should handle fund details error', fakeAsync(() => {
    (fundService.getFundById as jasmine.Spy).and.returnValue(throwError(() => new Error('API Error')));
    (portfolioCompanyService.getPortfolioMappedLpReportTemplates as jasmine.Spy).and.returnValue(of([]));
    
    component.ngOnInit();
    tick();

    expect(component.loading).toBeFalse();
  }));

  it('should handle edit redirect with permissions', () => {
    component.canEditFundDetails = true;
    component.model = { encryptedFundId: '123' };
    component.editRedirect();

    expect(router.navigate).toHaveBeenCalledWith(['/create-fund', '123']);
  });

  it('should show error when editing without permissions', () => {
    component.canEditFundDetails = false;
    component.editRedirect();

    expect(toastrService.error).toHaveBeenCalledWith(
      ErrorMessage.NoAccessFund,
      '',
      { positionClass: 'toast-center-center' }
    );
  });

  it('should handle track record value unit conversion', fakeAsync(() => {
    // Set up initial clone data
    component.fundTrackRecordsClone = [{
      totalInvestedCost: 1000,
      totalRealizedValue: 2000,
      totalUnRealizedValue: 3000,
      totalValue: 6000
    }];
    
    // Initialize empty array for results
    component.fundTrackRecords = [];
    
    component.trackRecordValueUnit = FinancialValueUnitsEnum.Millions;
    component.convertTrackRecordValueUnits();
    
    // Use tick to handle the setTimeout
    tick(20);

    // Now verify the results
    expect(component.fundTrackRecords.length).toBe(1);
    expect(component.fundTrackRecords[0].totalInvestedCost).toBe('1.00');
    expect(component.fundTrackRecords[0].totalRealizedValue).toBe('2.00');
    expect(component.fundTrackRecords[0].totalUnRealizedValue).toBe('3.00');
    expect(component.fundTrackRecords[0].totalValue).toBe('6.00');
  }));

  // Test for page change handling
  it('should handle page changes correctly', () => {
    component.deals = Array(150).fill({ id: 1 }); // Mock 150 deals
    component.pageChange({ skip: 100, take: 50 });
    
    expect(component.skip).toBe(100);
    expect(component.gridView.data.length).toBeLessThanOrEqual(component.pageSize);
  });

  // Test for search functionality
  it('should filter deals when searching', () => {
    component.dealsClone = [
      { portfolioCompanyDetails: { companyName: 'Test Company' }, dealCustomID: '001' },
      { portfolioCompanyDetails: { companyName: 'Another Company' }, dealCustomID: '002' }
    ];
    
    component.searchGrid('Test');
    
    expect(component.deals.length).toBe(1);
    expect(component.deals[0].portfolioCompanyDetails.companyName).toBe('Test Company');
  });

  // Test for permission checking
  it('should correctly check permission access', () => {
    const mockPermissions = [
      { CAN_VIEW: true, CAN_EDIT: false },
      { CAN_VIEW: true, CAN_EDIT: true }
    ];
    
    expect(component.checkPermissionAccess(mockPermissions, 'CAN_VIEW')).toBeTrue();
    expect(component.checkPermissionAccess(mockPermissions, 'CAN_EDIT')).toBeTrue();
  });

  it('should handle fund report export', fakeAsync(() => {
    component.model = { fundID: 1 };
    const mockBlob = new Blob(['mock pdf content'], { type: 'application/pdf' });
    (portfolioCompanyService.pdfExport as jasmine.Spy).and.returnValue(of(mockBlob));
    
    component.FundReport();
    tick();

    expect(portfolioCompanyService.pdfExport).toHaveBeenCalledWith({
      Value: 'FundReport',
      Id: component.fundId
    });
    expect(component.exportLoading).toBeFalse();
  }));

  it('should handle getMappedLpReportTemplates', fakeAsync(() => {
    const mockTemplates = [
      { templateId: 1, templateName: 'Template 1' },
      { templateId: 2, templateName: 'Template 2' }
    ];
    
    (portfolioCompanyService.getPortfolioMappedLpReportTemplates as jasmine.Spy)
      .and.returnValue(of(mockTemplates));
    
    component.getMappedLpReportTemplates();
    tick();

    expect(component.mappedLpReportTemplates).toEqual(mockTemplates);
    expect(portfolioCompanyService.getPortfolioMappedLpReportTemplates)
      .toHaveBeenCalledWith(component.fundId, component.page);
  }));

  it('should fetch chart data successfully', fakeAsync(() => {
    const reportService = TestBed.inject(ReportService);
    component.model = { fundID: 1 };
    
    component.getChartData();
    tick();

    expect(reportService.getReportData).toHaveBeenCalled();
    expect(component.chartDataLoading).toBeFalse();
    expect(component.fundPerformanceData).toBeDefined();
    expect(component.sectorwiseHoldingValues).toBeDefined();
  }));
  it('should create the default PDF download model with correct fields', () => {
    const mockTemplate: LpTemplateModel = { templateId: 123, templateName: 'Test Template' };
    component.fundId = 456;
    component.page = 'somePage';

    const result: PdfDownloadModel = component.createDefaultPdfDownloadModel(mockTemplate);
    expect(result.templateId).toBe(mockTemplate.templateId);
    expect(result.templateName).toBe(mockTemplate.templateName);
    expect(result.encryptedCompanyId).toBe('');
    expect(result.encryptedFundId).toBe('456');
    expect(result.downloadType).toBe('somePage');
  });

  it('should handle undefined fields in template without errors', () => {
    const mockTemplate: Partial<LpTemplateModel> = {};
    component.fundId = 999;
    component.page = 'anotherPage';

    const result: PdfDownloadModel = component.createDefaultPdfDownloadModel(mockTemplate as LpTemplateModel);
    expect(result.templateId).toBeUndefined();
    expect(result.templateName).toBeUndefined();
    expect(result.encryptedCompanyId).toBe('');
    expect(result.encryptedFundId).toBe('999');
    expect(result.downloadType).toBe('anotherPage');
  });
  it('should filter deals by event', () => {
    component.dealsClone = [
      { portfolioCompanyDetails: { companyName: 'AlphaCorp' }, dealCustomID: 'ABC123' },
      { portfolioCompanyDetails: { companyName: 'BetaCorp' }, dealCustomID: 'XYZ789' }
    ];
    component.searchGrid('Alpha');
    expect(component.deals.length).toBe(1);
    expect(component.deals[0].portfolioCompanyDetails.companyName).toBe('AlphaCorp');
  });

  it('should return all deals when event is empty', () => {
    component.dealsClone = [
      { portfolioCompanyDetails: { companyName: 'AlphaCorp' }, dealCustomID: 'ABC123' },
      { portfolioCompanyDetails: { companyName: 'BetaCorp' }, dealCustomID: 'XYZ789' }
    ];
    component.searchGrid('');
    expect(component.deals.length).toBe(2);
  });

  it("should show no access error when canEditFundDetails is false", () => {
    component.canEditFundDetails = false;
    spyOn(component, "showNoAccessError");
    component.editRedirect();
    expect(component.showNoAccessError).toHaveBeenCalled();
  });
  it("should return true for a numeric string", () => {
    expect(component.isNumberCheck("123")).toBe(true);
  });

  it("should return false for a non-numeric string", () => {
    expect(component.isNumberCheck("abc")).toBe(false);
  });
  it("should set width when onResized is called", () => {
    const mockEvent = { newRect: { width: 500 } };
    component.onResized(mockEvent);
    expect(component.width).toBe(500);
  });

  it("should sort array by the provided key", () => {
    const arr = [{ name: "b" }, { name: "a" }];
    const sorted = component.sortByKey(arr, "name");
    expect(sorted[0].name).toBe("a");
    expect(sorted[1].name).toBe("b");
  });
});
