import { FinancialsSubTabs } from "src/app/common/constants";
import { DataAuditLogValueModel } from "./kpiValueModel";
function getMonthNumber(monthName: string): number | undefined {
  const monthMap: { [key: string]: number } = {
    "Jan": 1,
    "Feb": 2,
    "Mar": 3,
    "Apr": 4,
    "May": 5,
    "Jun": 6,
    "Jul": 7,
    "Aug": 8,
    "Sep": 9,
    "Oct": 10,
    "Nov": 11,
    "Dec": 12
  };
  return monthMap[monthName];
}
const FinancialsSubTabsMap = {
  [FinancialsSubTabs.Actual]: "actual",
  [FinancialsSubTabs.Budget]: "budget",
  [FinancialsSubTabs.Forecast]: "forecast",
  [FinancialsSubTabs.IC]: "ic",
};
/**
 * Retrieves the corresponding tab name for cell editing based on the provided tab name.
 * @param tabName - The tab name to retrieve the corresponding cell edit tab name for.
 * @returns The corresponding cell edit tab name, or undefined if no match is found.
 */
export function getCellEditTabName(tabName: string): string | undefined {
  const result = FinancialsSubTabsMap[tabName];
  if (result === undefined) {
    return tabName;
  }
  return result;
}
/**
 * Extracts date components from the input string.
 * @param {string} input - The input string containing the date.
 * @returns {Object} - An object containing the extracted date components.
 * @property {string|null} year - The year component extracted from the input string.
 * @property {number} month - The month component extracted from the input string as a number (1-12).
 * @property {string|null} quarter - The quarter component extracted from the input string.
 */
export function extractDateComponents(input) {
  const year = input.match(/\d{4}/)[0];
  const month = input.match(/Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec/);
  const quarter = input.match(/Q[1-4]/);
  return {
    year: year || null,
    month: month ? getMonthNumber(month[0]) : 0,
    quarter: quarter ? quarter[0] : null
  };
}

/**
 * Validates a number input based on a KeyboardEvent.
 * @param event The KeyboardEvent object.
 */
export function validateNumber(event: KeyboardEvent): void {
  const target = event.target as HTMLInputElement;
  const value = target?.value;
  if (event.which !== 15) {
    const ex: RegExp = /^-*\d*(?:[.,]\d{1,6})?$/;
    if (!ex.test(value)) {
      if (value?.includes(".")) {
        let floatValue = parseFloat(value);
        if (floatValue % 1 === 0) {
          target.value = floatValue.toString();
        } else {
          target.value = floatValue.toFixed(6);
        }
      }
    }
  }
}
/**
 * Validates the maximum length of the input value based on specific conditions.
 * 
 * @param event - The keyboard event triggered by the user.
 * @returns A boolean value indicating whether the input value is valid or not.
 */
export function validateMaxLength(event: KeyboardEvent): boolean {
  const target = event.target as HTMLInputElement;
  const value = target?.value;

  if (value?.includes(".")) {
    if (value.length === 21) return false;
  } else {
    if (value.length === 16) return false;
  }

  return true;
}
/**
 * Converts a model object to FormData.
 * @param model The model object to convert.
 * @returns The FormData object containing the converted data.
 */
export function ModelToFormData(model: DataAuditLogValueModel): FormData {
  const formData = new FormData();
  Object.keys(model).forEach(key => {
    if (model[key] !== undefined && model[key] !== null) {
      formData.append(key, model[key].toString());
    }
  });
  return formData;
}