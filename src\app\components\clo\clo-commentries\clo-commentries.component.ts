import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-clo-commentries',
  templateUrl: './clo-commentries.component.html',
  styleUrls: ['./clo-commentries.component.scss']
})
export class CloCommentriesComponent  {
  constructor(private toastrService: ToastrService) {}
 
  @Input() commentarylist: any[];
  @Input() footnotes: any[];
  @Input() quillConfig: any;
  @Input() editorPlaceholder: string;
  @Output() save = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<any>();
  @Output() reset = new EventEmitter<any>();

  toggleEdit(clo: any) {
    clo.isEdit = !clo.isEdit;
  }

  expandPanel(clo: any) {
    this.footnotes.forEach(item => {
      if (item !== clo) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });
    clo.isExpanded = !clo.isExpanded;
  }
  onSave(clo: any) {
    this.save.emit(clo);
  }


  onCancel(clo: any): void {
   this.cancel.emit(clo);
  }

  onReset(clo: any): void {
    clo.newComment = '';
  }

}