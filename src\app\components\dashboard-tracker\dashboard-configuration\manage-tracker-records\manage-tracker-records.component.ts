import { Component, Input, OnChanges, OnInit, QueryList, SimpleChanges, ViewChildren } from '@angular/core';
import {ExpansionPanelComponent, ExpansionPanelActionEvent} from "@progress/kendo-angular-layout";
import { chevronUpIcon, SVGIcon } from "@progress/kendo-svg-icons";
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-manage-tracker-records',
  templateUrl: './manage-tracker-records.component.html',
  styleUrls: ['./manage-tracker-records.component.scss']
})
export class ManageTrackerRecordsComponent implements OnInit, OnChanges {
  @Input() isNewColumnAdded: boolean = false;
  @ViewChildren(ExpansionPanelComponent)
  panels: QueryList<ExpansionPanelComponent>;
  arrowUpIcon: SVGIcon = chevronUpIcon;
  showDropdownValues: number = 2;
  dropdownValueMaxLength: number = 10;
  showEditDeleteButton: boolean = false;
  expandedPanel: number = null;
  data: any[] = [];
  public maptoFields: { text: string, value: number }[] = DashboardConfigurationConstants.maptoFields;
  constructor(private readonly dashboardTrackerService: DashboardTrackerService) { }

  ngOnChanges(changes: SimpleChanges): void {
     if (changes['isNewColumnAdded'] && changes['isNewColumnAdded'].currentValue !== changes['isNewColumnAdded'].previousValue) {
        this.UpdateColumnsCollection();
     }
  }

ngOnInit(): void {
  this.UpdateColumnsCollection();
}
  private UpdateColumnsCollection() {
    this.dashboardTrackerService.getDashboardTrackerColumnData().subscribe({
      next: (res) => {
        this.data = res.map((item: any) => ({
          fieldType: item.fieldTypeName,
          dataType: item.dataTypeName,
          namePattern: item.name,
          createdDate: item.createdOn ? this.formatDate(item.createdOn) : '',
          mapTo: item.mapTo ? this.maptoFields.find(i => i.value === item.mapTo).text : '',
          dropdownList: item.dropdownList ?? undefined,
          isEnable: item.isActive
        }));
      },
      error: (err) => {
        console.error('Error fetching column data', err);
      }
    });
  }

private formatDate(dateString: string): string {
  const date = new Date(dateString);
  return !dateString || isNaN(date.getTime())
    ? ''
    : `${date.getDate().toString().padStart(2, '0')}/${date.toLocaleString('en-US', { month: 'short' })}/${date.getFullYear().toString().slice(-2)}`;
}
  onToggleChange(event: any, item: any): void {
    item.isEnable = event;
  }

  onToggleClick(event: any, item: any): void {
    event.stopPropagation();
  }

  public onAction(ev: ExpansionPanelActionEvent, index: number): void {
    this.panels.forEach((panel, idx) => {
      if (idx !== index && panel.expanded) {
        panel.toggle();
      }
      if(idx === index && ev.action === 'expand'){
        this.showEditDeleteButton = true;
        this.expandedPanel = index;
      }
      if(idx === index && ev.action === 'collapse'){
        this.showEditDeleteButton = false;
      }
    });
  }
}
