<app-loader-component *ngIf="isLoading"></app-loader-component>


<div class="kendo-container">
  <kendo-grid id="dashboardTrackerTable" class="dashboard-tracker-table" [ngClass]="passedClass" [data]="view | async"
    [scrollable]="'scrollable'" [sortable]="true" [style.min-width.px]="gridColumns.length * 200 + 400"
    [pageSize]="state.take" [skip]="state.skip" [pageable]="{
      buttonCount: 10,
      info: true,
      type: 'numeric',
      pageSizes: [100, 200, 300],
      previousNext: true
    }" (dataStateChange)="dataStateChange($event)">

    <ng-container *ngFor="let col of gridColumns; let i = index">

      <kendo-grid-column *ngIf="col.name === 'Fund Name'" [name]="col.name" [title]="col.name" [width]="200">       
        <ng-template kendoGridCellTemplate let-dataItem>            
             {{ dataItem['Fund Name'] }}
        </ng-template>
      </kendo-grid-column>
      <!-- Special handling for Portfolio Company column (with logo and name) -->       
      <kendo-grid-column *ngIf="col.name === 'Portfolio Company Name'" [name]="col.name" [title]="col.name" [width]="300">       
        <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex align-items-center">
              <ng-container *ngIf="dataItem.CompanyLogo && dataItem.CompanyLogo.trim() !== ''">
                <img [src]="dataItem.CompanyLogo" alt="logo" class="company-logo mr-2 p-1" />                
              </ng-container>
              <ng-container *ngIf="!dataItem.CompanyLogo || dataItem.CompanyLogo.trim() === ''">
                <span class="text-logo mr-2">{{ dataItem['Portfolio Company Name']?.slice(0,1) }}</span>
              </ng-container>
              <span>{{ dataItem['Portfolio Company Name'] }}</span>
            </div>
        </ng-template>
      </kendo-grid-column>

      <!-- All other columns -->
      <kendo-grid-column *ngIf="col.name !== 'Portfolio Company Name' && col.name !== 'CompanyLogo' && col.name !== 'Fund Name' && col.name !== 'PCID' && col.name !== 'FundID' " [name]="col.name"
        [title]="col.name" [width]="200">
        <ng-template *ngIf="isDashboardConfigurationTab" kendoGridHeaderTemplate>
          <input type="checkbox" kendoCheckBox />
          <span class="Body-R text-truncate header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <!-- Dashboard Configuration Tab - Special rendering -->
          <ng-container *ngIf="isDashboardConfigurationTab">

            <!-- Regular cell for columns with mapTo -->
            <ng-container *ngIf="!col.isDropDown && col.mapTo">
              {{ dataItem[col.name] || '' }}
            </ng-container>
            <!-- Dropdown for columns with isDropDown -->
            <ng-container *ngIf="col.isDropDown">
              <div class="dropdown-input">
                <kendo-combobox id="dropdown-input-{{ i }}" class="k-custom-solid-dropdown k-dropdown-height-32"
                  [data]="col.dropDownValues" 
                  valueField="value"
                  textField="displayText" 
                  [clearButton]="false" 
                  [rounded]="'medium'" [fillMode]="'solid'"
                  placeholder="Select Here">
                </kendo-combobox>
              </div>
            </ng-container>
            
            
            
            <!-- Textbox for other columns -->
            <ng-container *ngIf="!col.isDropDown && !col.mapTo">
              <kendo-textbox 
                fillMode="flat"
                [placeholder]="col.dataType === 3 ? defaultDateFormat : ''"
                [value]="dataItem[col.name] || ''"
                >
                <ng-template kendoTextBoxSuffixTemplate>
                    <button kendoButton fillMode="clear">
                     <img src="assets/dist/images/close-clear.svg">
                    </button>
                </ng-template>
              </kendo-textbox>
            </ng-container>
          </ng-container>
          
          <!-- Regular view - existing behavior -->
          <ng-container *ngIf="!isDashboardConfigurationTab">
            {{ dataItem[col.name] || '' }}
          </ng-container>
        </ng-template>
      </kendo-grid-column>
    </ng-container>    
    <!-- default screen if grid data is empty -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="text-center py-5 mt-5" *ngIf="totalRecords === 0">
        <img src="assets/dist/images/Illustrations.svg" alt="No data" class="mb-3" />
        <p class="mb-0 Body-R content-secondary">No Data Found</p>
      </div>
    </ng-template>
  </kendo-grid>
</div>
