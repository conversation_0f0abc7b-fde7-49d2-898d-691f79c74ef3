import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DashboardConfigurationConstants } from 'src/app/common/constants';
import { DashboardTrackerComponent } from '../dashboard-tracker.component';

@Component({
  selector: 'app-dashboard-configuration',
  templateUrl: './dashboard-configuration.component.html',
  styleUrls: ['./dashboard-configuration.component.scss']
})
export class DashboardConfigurationComponent implements OnInit {
  @ViewChild('dashboardTracker') dashboardTrackerComponent!: DashboardTrackerComponent;

  DashboardConfigurationTab: string = DashboardConfigurationConstants.DashboardConfigurationTab;
  ManageTrackerFieldsTab: string = DashboardConfigurationConstants.ManageTrackerFieldsTab;
  DeletedColumnTab: string = DashboardConfigurationConstants.DeletedColumnTab;
  StatusFilterTab: string = DashboardConfigurationConstants.StatusFilterTab;

  isDashboardConfigurationTab: boolean = true;

  tabList: ITab[] = [
    { name: this.DashboardConfigurationTab, active: true },
    { name: this.ManageTrackerFieldsTab, active: false },
    { name: this.DeletedColumnTab, active: false },
    { name: this.StatusFilterTab, active: false }
  ];
  selectedTab: ITab = this.tabList[0];
  pendingChangesCount: number = 0;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {    
  }

  onTabClick(tab: ITab) {
    this.tabList.forEach(t => t.active = false);
    tab.active = true;
    this.selectedTab = tab;
  }

  navigateToDashboardConfig() {
    this.tabList.forEach(t => t.active = false);
    this.tabList[1].active = true;
    this.selectedTab = this.tabList[1];
  }

  // Handle cell changes from dashboard tracker
  onCellChangesUpdated(count: number): void {
    this.pendingChangesCount = count;
    this.cdr.detectChanges();
  }

  // Check if save button should be disabled
  isSaveDisabled(): boolean {
    return this.pendingChangesCount === 0;
  }

  // Handle save button click
  onSaveClick(): void {
    if (this.dashboardTrackerComponent && this.dashboardTrackerComponent.getPendingChangesCount() > 0) {
      this.dashboardTrackerComponent.saveCellValues();
    }
  }

  // Handle cancel button click
  onCancelClick(): void {
    if (this.dashboardTrackerComponent) {
      this.dashboardTrackerComponent.clearPendingChanges();
    }
  }
}
