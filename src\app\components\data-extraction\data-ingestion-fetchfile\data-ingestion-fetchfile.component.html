<div class="data-extraction-container">
    <div class="left-content">
        <div class="company-info-container">
            <div class="company-header">
                <div class="logo" [ngClass]="!isLogo ? 'background-circle-bg0 logo-bg-color' : ''">
                    <div *ngIf="!isLogo">
                        {{ companyInitials }}
                    </div>
                    <img *ngIf="isLogo" class="logo" [src]="companyLogo" alt="" />
                </div>
                <div class="company-details">
                    <div class="company-name-fetch-file" [ngSwitch]="processDetails?.extractionType">
                        <ng-container *ngSwitchCase="'Specific KPI'">{{processDetails?.fundName}}</ng-container>
                        <ng-container *ngSwitchDefault>{{companyName}}</ng-container>
                    </div>
                    <div class="extra-info">
                        <span class="reportingPeriod">{{reportingPeriod}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="action-buttons">
        <button [disabled]="!gridForm.valid || selectedFilesList?.length == 0"  kendoButton class="kendo-custom-button Body-R apply-btn mr-2 d-none" fillMode="outline"
            themeColor="primary">
            Save as Draft
        </button>
        <button (click)="extract()" [disabled]="!gridForm.valid || selectedFilesList?.length == 0"  class="kendo-custom-button Body-R apply-btn" themeColor="primary" kendoButton>
            <img src='assets/dist/images/ai.svg' alt="" class="extract-icon" />
            Extract
        </button>
    </div>
</div>
<div class="documents-container">
    <div class="title-section">
        Documents
    </div>
    <div class="button-section" *ngIf="processDetails?.extractionType !== 'Specific KPI'">
        <button class="upload-button" kendoButton (click)="fetchFileInput.click()">
            <img src='assets/dist/images/uploadMore.svg' alt="upload icon" class="left-icon">
            <span class="button-text Body-R">Upload File</span>
        </button>
        <input #fetchFileInput type="file" multiple accept=".pdf" class="hidden-file-input" (change)="onFileSelected($event)">
    </div>
</div>
<div class="row mr-0 ml-0 ">
    <div class="col-12 pr-0 pl-0">
        <form [formGroup]="gridForm">
            <div kendoPopoverContainer [popover]="popover" filter=".has-popover" #container="kendoPopoverContainer" showOn="none">
            <kendo-grid [data]="selectedFilesList" [resizable]="true" [sortable]="false" style="width: 100%;"
                class="k-grid-extraction-custom custom-ingestion-list-fetchfile-grid custom-ingestion-preview-grid" [scrollable]="'scrollable'">
                <kendo-grid-column field="name" title="File Name" [width]="300" [sortable]="false">
    <ng-template kendoGridHeaderTemplate let-dataItem>
        <div class="d-flex align-items-center">
            <input kendoCheckBox 
                type="checkbox" 
                class="k-checkbox k-checkbox-md k-rounded-md chkbx-border mr-3"
                [checked]="areAllDocumentsSelected()"
                (click)="toggleAllDocumentsSelection()"
                (keydown.space)="toggleAllDocumentsSelection()">
            <span>File Name</span>
        </div>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
        <div [formArrayName]="'files'">
            <div [formGroupName]="rowIndex" title="{{ dataItem.name }}" class="file-name text-truncate d-flex align-items-center">
                <input kendoCheckBox 
                    type="checkbox" 
                    class="k-checkbox k-checkbox-md k-rounded-md chkbx-border mr-3"
                    [checked]="dataItem.selected"
                    (click)="toggleDocumentSelection(dataItem,rowIndex)"
                    (keydown.space)="toggleDocumentSelection(dataItem,rowIndex)"
                    [formControl]="formArray.at(rowIndex).get('selected')">
                <img *ngIf="dataItem.errors.length>0" id="error_{{rowIndex}}" class="data-extraction-icon"
                    src='assets/dist/images/extraction_error_icon.svg' alt="Error"  (click)="showFileDetails($event, dataItem)"
                    (keydown)="showFileDetails($event, dataItem)" />
                <a class="text-truncate" href="javascript:void" (click)="setPdfSource(dataItem,rowIndex)">
                    <img [src]="getFileExtensions(dataItem.name).toLowerCase() === 'xlsx' ? 
                    'assets/dist/images/xls-icon.svg' : 
                    'assets/dist/images/pdf-icon.svg'" alt="PDF" class="pdf-icon" />
                    <span class="ml-2">{{ dataItem.name }}</span>
                </a>
            </div>
        </div>
    </ng-template>
</kendo-grid-column>
                <kendo-grid-column field="DocumentType" title="Document Type" [width]="200" [sortable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="saved-info">
                            <div class="document-type">
                                <ng-container [ngSwitch]="formArray.at(rowIndex).get('documentType')?.value">
                                    <ng-container *ngSwitchCase="null"><span class="text-color">Select Document Type</span></ng-container>                                    <ng-container *ngSwitchDefault>{{formArray.at(rowIndex).get('documentType')?.value?.documentName}}</ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="Period" title="Period" [width]="150" [sortable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="saved-info">
                            <div class="document-type">
                                <ng-container *ngIf="!formArray.at(rowIndex).get('documentType')?.value">
                                    <span class="text-color">Select Period</span>
                                </ng-container>
                                <ng-container *ngIf="formArray.at(rowIndex).get('documentType')?.value">
                                    <ng-container [ngSwitch]="formArray.at(rowIndex).get('periodType')?.value?.name">
                                        <ng-container *ngSwitchCase="'Month'">
                                            {{formArray.at(rowIndex).get('month')?.value?.name}},{{formArray.at(rowIndex).get('year')?.value?.name}}
                                        </ng-container>
                                        <ng-container *ngSwitchCase="'Quarter'">
                                            {{formArray.at(rowIndex).get('quarter')?.value?.text}},{{formArray.at(rowIndex).get('year')?.value?.name}}
                                        </ng-container>
                                        <ng-container *ngSwitchCase="'Year'">
                                            {{formArray.at(rowIndex).get('year')?.value?.name}}
                                        </ng-container>
                                        <ng-container *ngSwitchDefault>
                                            <ng-container *ngIf="formArray.at(rowIndex).get('periodType')?.value === null">
                                                <span class="text-color">No Period Selected</span>
                                            </ng-container>
                                            <ng-container *ngIf="formArray.at(rowIndex).get('periodType')?.value !== null">
                                                <span class="text-color">Select Period</span>
                                            </ng-container>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="pages" [width]="500" [minResizableWidth]="500" [sortable]="false" [headerClass]="'k-grid-header-req'"
                *ngIf="processDetails && processDetails.extractionType !== 'Specific KPI'">
                <ng-template kendoGridHeaderTemplate>
                    <span class="k-column-title ng-star-inserted">Pages</span>
                    <img src="assets/dist/images/info-icon.svg"
                         alt="Information about pages requirement"
                         class="ml-2 mt-1"
                         style="width: 16px; cursor: pointer;"
                         kendoTooltip
                         title="At least one input is needed for below categories"/>
                </ng-template>
                 <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div [formArrayName]="'files'" class="files-container">
                      <div [formGroupName]="rowIndex" class="pages-container">
                        <div formArrayName="pages" class="page-section">
                            <div class="mr-2 d-inline-block page-text" *ngFor="let pageGroup of formArray.at(rowIndex).get('pages').controls; let i = index" [formGroupName]="i">
                                <label for="pagesInput_{{rowIndex}}_{{i}}" class="Caption-R d-flex mt-0 mb-1">{{pageGroup.get('alias').value}}</label>
                                <kendo-textbox appNumbersCommaOnly  id="pagesInput_{{rowIndex}}_{{i}}" text="Large" class="custom-kendo-text-box" fillMode="solid" [clearButton]="false"
                                formControlName="items"  placeholder="Pages (1,3,6-9)" 
                                (input)="onPagesInputChange($event, rowIndex, i)"
                                >
                                </kendo-textbox>
                            </div>
                          </div>
                      </div>
                      </div>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column title="Actions" [width]="100" [sortable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                       <div class="w-100 text-center"> <img class="close-icon"  id="{{rowIndex}}" src='assets/dist/images/delete.svg' alt="Delete" (click)="deleteFile(rowIndex,dataItem.id)" (keydown)="deleteFile(rowIndex,dataItem.id)" /></div>
                    </ng-template>
                </kendo-grid-column>

                <ng-template kendoGridNoRecordsTemplate>
                    <ng-container *ngTemplateOutlet="noDataTemplate;"></ng-container>
                </ng-template>
            </kendo-grid>
            </div>
        </form>
    </div>
</div>
<ng-template #noDataTemplate>
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/no-content-lp-report.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-M">
            No files uploaded.
        </div>
        <div class="text-center no-content-sub Caption-M template-text break-word">
         Click on “Replace File” button to start extraction
        </div>
    </div>
</ng-template>
<kendo-popover #popover position="right" [width]="402" [callout]="false">
    <ng-template kendoPopoverTitleTemplate  >
        <div class="row mr-0 ml-0">
            <div class="col-md-12 pr-0 pl-0">
                <div class="float-left M-M pt-1" title="Error Message">Error Message</div>
                <span class="float-right close-icon" (click)="onClose()" (keydown)="onClose()">
                    <img src='assets/dist/images/cross-x-icon.svg' alt="" />
                </span>
            </div>
        </div>
    </ng-template>
    <ng-template kendoPopoverBodyTemplate>
        <div class="row mr-0 ml-0 error-body-message Body-R">
            <div class="col-md-12 pr-0 pl-0">
                <div class="error-message-container Body-R" *ngFor="let data of selectedFileErrors">
                    <div class="error-message">{{data}} Please delete file and upload again.</div>
                </div>
            </div>
        </div>
    </ng-template>
</kendo-popover>
<div *ngIf="showSelectionPopup" class="selection-popup">
    <div class="selection-content pt-2 pb-2 pr-4 pl-4">
        <form [formGroup]="selectionForm">
            <div class="d-flex align-items-center justify-content-between">
                <!-- Left side - Dropdowns -->
                <div class="d-flex align-items-center custom-gap">
                    <!-- Document Type -->
                    <div class="dropdown-group d-flex align-items-center">
                        <label class="S-R me-2 mb-0 pr-2 ">Document Type:</label>
                        <kendo-combobox
                            [data]="documentTypes"
                            [textField]="'documentName'"
                            [valueField]="'id'"
                            [clearButton]="false"
                            formControlName="documentType"
                            [fillMode]="'solid'"
                            [size]="'medium'"
                            placeholder="Eg:Board Pack etc."
                            class="k-custom-solid-dropdown"
                            (valueChange)="onDocumentTypeChange($event)"
                            (filterChange)="filterDocumentTypes($event)">
                        </kendo-combobox>
                    </div>
               
                    <!-- Period Type -->
                    <div class="dropdown-group d-flex align-items-center">
                        <label class="S-R me-2 mb-0 pr-2">Period Type:</label>
                        <kendo-combobox
                            [data]="periodTypes"
                            textField="name"
                            valueField="id"
                            formControlName="periodType"
                            [fillMode]="'solid'"
                            [size]="'medium'"
                            placeholder="Choose Type"
                            class="k-custom-solid-dropdown"
                            (valueChange)="onPeriodTypeChange($event)"
                            [clearButton]="false">
                        </kendo-combobox>
                    </div>
               
                    <!-- Year -->
                    <div class="dropdown-group d-flex align-items-center">
                        <label class="S-R me-2 mb-0 pr-2">Year:</label>
                        <kendo-combobox
                            [data]="availableYears"
                            textField="name"
                            valueField="id"
                            formControlName="year"
                            [fillMode]="'solid'"
                            [size]="'medium'"
                            placeholder="Choose Year"
                            class="k-custom-solid-dropdown"
                            (valueChange)="onYearChange($event)" [clearButton]="false"
                           >
                        </kendo-combobox>
                    </div>
               
                    <!-- Month -->
                    <div class="dropdown-group d-flex align-items-center"
                         *ngIf="selectionForm.get('periodType')?.value?.id === 'month' || selectionForm.get('periodType')?.value === 'month'">
                        <label class="S-R me-2 mb-0 pr-2">Month:</label>
                        <kendo-combobox
                            [data]="availableMonths"
                            textField="name"
                            valueField="id"
                            formControlName="month"
                            [fillMode]="'solid'"
                            [size]="'medium'"
                            placeholder="Choose Month"
                            class="k-custom-solid-dropdown" [clearButton]="false">
                        </kendo-combobox>
                    </div>

                    <!-- Quarter -->
                    <div class="dropdown-group d-flex align-items-center"
                         *ngIf="selectionForm.get('periodType')?.value?.id === 'quarter' || selectionForm.get('periodType')?.value === 'quarter'">
                        <label class="S-R me-2 mb-0 pr-2">Quarter:</label>
                        <kendo-combobox
                            [data]="availableQuarters"
                            textField="text"
                            valueField="id"
                            formControlName="quarter"
                            [fillMode]="'solid'"
                            [size]="'medium'"
                            placeholder="Choose Quarter"
                            class="k-custom-solid-dropdown" [clearButton]="false">
                        </kendo-combobox>
                    </div>
                </div>
 
                <!-- Right side - Buttons -->
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline" kendoButton
                    (click)="closeSelectionPopup()" themeColor="primary">Cancel</button>
               
                    <button [disabled]="!selectionForm.valid" (click)="applyBulkChanges()" class="kendo-custom-button Body-R apply-btn" kendoButton
                    themeColor="primary">Save</button>
                    
                    <img src='assets/dist/images/cross-x-icon.svg' alt="Close" class="close-icon pl-4 cross-icon"  (click)="closeSelectionPopup()" />
                </div>
            </div>
        </form>
    </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>
<app-pdf-preview *ngIf="showPreview"  [modules]="modules" [pdfSource]="pdfSource" (close)="showPreview = false;"></app-pdf-preview>

