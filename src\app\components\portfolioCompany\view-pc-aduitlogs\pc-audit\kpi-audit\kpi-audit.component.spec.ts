/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { KpiAuditComponent } from './kpi-audit.component';
import { AuditService } from 'src/app/services/audit.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of, throwError } from 'rxjs';
import { DataAuditDto, PcAuditDocumentModel } from '../../models/kpi-audit-model';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { SharedPipeModule } from 'src/app/custom-modules/shared-pipe.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { HttpResponse } from '@angular/common/http';
describe('KpiAuditComponent', () => {
  let component: KpiAuditComponent;
  let fixture: ComponentFixture<KpiAuditComponent>;
  let mockAuditService: Partial<AuditService>;
  let mockMiscService: Partial<MiscellaneousService>;
  beforeEach(async(() => {
    mockAuditService = {
      getPcAuditLogData: () => of({
        pcAudits: [],
        auditInfo: null
      }),
      exportZipFile: () => of(null),
      exportDocFile: () => of(null),
      getSupportingDocs: () => of([]),
      getComment: () => of(null)
    };

    mockMiscService = {
      downloadAllFormatFile: () => {},
      closePopup: () => {}
    };
    TestBed.configureTestingModule({
      imports: [SharedPipeModule,
        CommonModule,
        FormsModule,
        PrimeNgModule,
        SharedComponentModule,
        SharedDirectiveModule,],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ KpiAuditComponent ],
      providers: [
        { provide: AuditService, useValue: mockAuditService },
        { provide: MiscellaneousService, useValue: mockMiscService }
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(KpiAuditComponent);
    component = fixture.componentInstance;
    component.kpiAuditModel = {
      KpiId: 1,
      ModuleId: 1,
      AttributeId: 1,
      CompanyId: 1,
      ValueType: "Actual",
      Period: "Q1 2020",
      PeriodId: 1,
      ColumnKpiId: 1,
      ColumnKpi: "Ebitda",
      AsOfPeriod: "Q1 2020",
      ColumnKpiInfo: "USD",
      SectionId: 1
    };
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should retrieve audit logs successfully', () => {
    // Arrange
    const mockData: DataAuditDto = {
      pcAudits: [], // provide necessary mock data
      auditInfo: null // provide necessary mock data
    };
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getPcAuditLogData').and.returnValue(of(mockData));

    // Act
    component.getAuditLogs();

    // Assert
    expect(component.auditLogData).toEqual([]);
    expect(component.auditInfo).toBeNull();

    // Simulate successful response
    expect(component.isLoading).toBeFalse();
  });

  it('should handle error while retrieving audit logs', () => {
    // Arrange
    const error = 'Error message';
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getPcAuditLogData').and.returnValue(throwError(error));

    // Act
    component.getAuditLogs();

    // Assert
    expect(component.auditLogData).toEqual([]);
    expect(component.auditInfo).toBeNull();
  });
  it('should calculate sideNavWidth and leftWidth correctly', () => {
    // Arrange
    const sideNavWidth = '4.68em';

  
    // Assert
    expect(component.sideNavWidth).toBe('calc(100% - 4.68em - 20px)');
    expect(component.leftWidth).toBe('32px');
  });
  
  it('should calculate sideNavWidth and leftWidth correctly when sideNavWidth is not "4.68em"', () => {
    // Arrange
    const sideNavWidth = '5em';
  
  
    // Assert
    expect(component.sideNavWidth).toBe('calc(100% - 5em - 20px)');
    expect(component.leftWidth).toBe('120px');
  });
  it('should open the popup', () => {


    // Act
    component.openPopUp();

    // Assert
    expect(component.isOpenPopup).toBeTrue();
  });

  // it('should download zip file successfully', () => {
  //   // Arrange
  //   const currentDate = new Date();
  //   component.auditInfo = {
  //     moduleName: 'Trading Records',
  //     kpiId: 1,
  //     kpiInfo:"#",
  //     kpi:"Ebitda",
  //     currencyCode:"USD"
  //   }
  //   const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
  //   spyOn(auditService, 'exportZipFile').and.returnValue(of(new HttpResponse<Blob>({
  //     body: new Blob(),
  //     headers: new HttpHeaders(),
  //     status: 200,
  //     statusText: 'OK',
  //     url: 'http://example.com'
  //   })));

  //   // Act
  //   component.downloadZip();

  //   // Assert
  // });

  it('should handle error while downloading zip file', () => {
    // Arrange
    const error = 'Error message';
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'exportZipFile').and.returnValue(throwError(error));

    // Act
    component.downloadZip();

  
    // Act
    component.downloadZip();
  
    // Assert
    expect(component.isDocLoading).toBeFalse();
  });
  it('should return true if the input is a number', () => {
    // Arrange
    const input = 123;
  
    // Act
    const result = component.isNumberCheck(input);
  
    // Assert
    expect(result).toBeTrue();
  });
  
  it('should return false if the input is not a number', () => {
    // Arrange
    const input = 'abc';
  
    // Act
    const result = component.isNumberCheck(input);
  
    // Assert
    expect(result).toBeFalse();
  });
  it('should get comment successfully', () => {
    // Arrange
    const commentId = 1;
    const mockData = { comments: 'Test comment' };
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getComment').and.returnValue(of(mockData));
  
    // Act
    component.getComment(commentId);
  
    // Assert
    expect(component.commentText).toEqual('Test comment');
    expect(component.isLoading).toBeFalse();
  });
  
  it('should handle error while getting comment', () => {
    // Arrange
    const commentId = 1;
    const error = 'Error message';
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getComment').and.returnValue(throwError(error));
  
    // Act
    component.getComment(commentId);
  
    // Assert
    expect(component.isLoading).toBeFalse();
  });
  it('should get supporting documents successfully', () => {
    // Arrange
    const supportingDocIds = '1,2,3';
    const mockData: PcAuditDocumentModel[] = [
      { documentId: "1", documentName: 'Document 1' ,extension: 'pdf'},
      { documentId: "2", documentName: 'Document 2',extension: 'pdf' },
      { documentId: "3", documentName: 'Document 3' ,extension: 'pdf'}
    ];
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getSupportingDocs').and.returnValue(of(mockData));
  
    // Act
    component.getSupportingDocuments(supportingDocIds);
  
    // Assert
    expect(component.documentData).toEqual(mockData);
    expect(component.isLoading).toBeFalse();
  });
  
  it('should handle error while getting supporting documents', () => {
    // Arrange
    const supportingDocIds = '1,2,3';
    const error = 'Error message';
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getSupportingDocs').and.returnValue(throwError(error));
  
    // Act
    component.getSupportingDocuments(supportingDocIds);
  
    // Assert
    expect(component.isLoading).toBeFalse();
  });
  it('should open comments popup', () => {
    // Arrange
    const commentId = 1;
    spyOn(component, 'getComment');
    spyOn(mockMiscService, 'closePopup');
  
    // Act
    component.openCommentsPopUp(commentId);
  
    // Assert
    expect(component.commentText).toEqual('');
    expect(mockMiscService.closePopup).toHaveBeenCalled();
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeFalse();
    expect(component.isComments).toBeTrue();
    expect(component.getComment).toHaveBeenCalledWith(commentId);
  });
  
  it('should open comments popup without calling getComment', () => {
    // Arrange
    const commentId = null;
    spyOn(component, 'getComment');
  
    // Act
    component.openCommentsPopUp(commentId);
  
    // Assert
    expect(component.commentText).toEqual('');
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeFalse();
    expect(component.isComments).toBeTrue();
    expect(component.getComment).not.toHaveBeenCalled();
  });
  it('should open document popup and retrieve supporting documents successfully', () => {
    // Arrange
    const documentIds = '1,2,3';
    const mockData: PcAuditDocumentModel[] = [
      { documentId: "1", documentName: 'Document 1', extension: 'pdf' },
      { documentId: "2", documentName: 'Document 2', extension: 'pdf' },
      { documentId: "3", documentName: 'Document 3', extension: 'pdf' }
    ];
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'getSupportingDocs').and.returnValue(of(mockData));
  
    // Act
    component.openDoc(documentIds);
  
    // Assert
    expect(component.documentData).toEqual(mockData);
    expect(component.isLoading).toBeFalse();
  });
  
  it('should open document popup without retrieving supporting documents', () => {
    // Arrange
    const documentIds = null;
    spyOn(component, 'getSupportingDocuments');
  
    // Act
    component.openDoc(documentIds);
  
    // Assert
    expect(component.documentData).toEqual([]);
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeTrue();
    expect(component.isComments).toBeFalse();
    expect(component.getSupportingDocuments).not.toHaveBeenCalled();
  });
  it('should download file successfully', () => {
    // Arrange
    const fileId = '1';
    const fileName = 'test-file.pdf';
    const mockResponse = {}; // Provide a mock response object
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    const miscellaneousServiceStub: MiscellaneousService = fixture.debugElement.injector.get(MiscellaneousService);
    spyOn(auditService, 'exportDocFile').and.returnValue(of(new HttpResponse<Blob>({ body: new Blob() })));
    // Assert
    spyOn(miscellaneousServiceStub, 'downloadAllFormatFile');

    // Act
    component.downloadFile(fileId, fileName);

    // Assert
    expect(component.isDocSupportLoading).toBeFalse();
  });
  
  it('should handle error while downloading file', () => {
    // Arrange
    const fileId = '1';
    const fileName = 'test-file.pdf';
    const error = 'Error message';
    const auditService: AuditService = fixture.debugElement.injector.get(AuditService);
    spyOn(auditService, 'exportDocFile').and.returnValue(throwError(error));
    spyOn(console, 'log');
  
    // Act
    component.downloadFile(fileId, fileName);
  
    // Assert
    expect(component.isDocSupportLoading).toBeFalse();
    expect(console.log).toHaveBeenCalledWith(error);
  });
});
