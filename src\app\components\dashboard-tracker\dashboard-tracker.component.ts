import { Component, OnInit, Input } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { Observable, of } from 'rxjs';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { State } from '@progress/kendo-data-query';
import { DashboardCellValueDto, SaveDashboardCellValuesDto } from './model/dashboard-tracker-config.model';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridColumns: any[] = [];
  totalRecords: number = 0;
  defaultDateFormat: string = 'MM/DD/YYYY'; // placeholder text display only
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;

  // Collection to store cell value changes
  cellValueChanges: DashboardCellValueDto[] = [];

  constructor(
    private dashboardTrackerService: DashboardTrackerService,
  ) {}

  ngOnInit(): void {
    this.loadDashboardTableData(this.state);
  }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    this.dashboardTrackerService.getDashboardTableData(filter).subscribe((response) => {
      if (response && response.data && response.columns) {
        this.gridColumns = response.columns;
        this.totalRecords = response.totalRecords || 0;
        this.view = of<GridDataResult>({
          data: response.data,
          total: this.totalRecords
        });        
      } else {
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
      this.isLoading = false;
    });
  }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

  navigateToDashboardConfig(): void {

  }

  // Handle dropdown value changes
  onDropdownValueChange(value: any, dataItem: any, column: any): void {
    this.updateCellValue(dataItem, column, value?.value || value);
  }

  // Handle textbox value changes
  onTextboxValueChange(value: string, dataItem: any, column: any): void {
    this.updateCellValue(dataItem, column, value);
  }

  // Update or add cell value to the collection
  private updateCellValue(dataItem: any, column: any, newValue: any): void {
    const portfolioCompanyId = dataItem['PCID'];
    const fundId = dataItem['FundID'];
    const columnId = column.id;
    const timeSeriesId = column.timeSeriesId || null;

    // Validate required fields
    if (!portfolioCompanyId || !fundId || !columnId) {
      console.warn('Missing required IDs for cell value update:', {
        portfolioCompanyId,
        fundId,
        columnId,
        dataItem,
        column
      });
      return;
    }

    // Find existing entry or create new one
    const existingIndex = this.cellValueChanges.findIndex(
      item => item.PortfolioCompanyId === portfolioCompanyId &&
              item.FundId === fundId &&
              item.ColumnId === columnId &&
              item.TimeSeriesID === timeSeriesId
    );

    const cellValueDto: DashboardCellValueDto = {
      PortfolioCompanyId: portfolioCompanyId,
      FundId: fundId,
      ColumnId: columnId,
      TimeSeriesID: timeSeriesId,
      CellValue: newValue?.toString() || ''
    };

    if (existingIndex >= 0) {
      // Update existing entry
      this.cellValueChanges[existingIndex] = cellValueDto;
    } else {
      // Add new entry
      this.cellValueChanges.push(cellValueDto);
    }

    // Update the grid data item for immediate UI feedback
    dataItem[column.name] = newValue;

    console.log('Cell value updated:', cellValueDto);
    console.log('Total pending changes:', this.cellValueChanges.length);
  }

  // Save all cell value changes
  saveCellValues(): void {
    if (this.cellValueChanges.length === 0) {
      console.log('No changes to save');
      return;
    }

    const payload: SaveDashboardCellValuesDto = {
      CellValues: this.cellValueChanges
    };

    this.dashboardTrackerService.saveDashboardCellValues(payload).subscribe({
      next: (response) => {
        console.log('Cell values saved successfully', response);
        // Clear the changes collection after successful save
        this.cellValueChanges = [];
      },
      error: (error) => {
        console.error('Error saving cell values', error);
      }
    });
  }

  // Get pending changes count for UI display
  getPendingChangesCount(): number {
    return this.cellValueChanges.length;
  }

  // Clear all pending changes
  clearPendingChanges(): void {
    this.cellValueChanges = [];
    console.log('All pending changes cleared');
  }

  // Get current changes for debugging
  getCurrentChanges(): DashboardCellValueDto[] {
    return [...this.cellValueChanges];
  }
}
