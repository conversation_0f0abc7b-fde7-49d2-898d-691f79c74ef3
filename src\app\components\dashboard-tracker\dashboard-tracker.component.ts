import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { Observable, of, Subject } from 'rxjs';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { State } from '@progress/kendo-data-query';
import { DashboardCellValueDto, SaveDashboardCellValuesDto } from './model/dashboard-tracker-config.model';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit, OnDestroy {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  @Output() cellChangesUpdated = new EventEmitter<number>();
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridColumns: any[] = [];
  totalRecords: number = 0;
  defaultDateFormat: string = 'DD/MM/YYYY'; // placeholder text display only
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;

  // Collection to store cell value changes
  cellValueChanges: DashboardCellValueDto[] = [];

  // Subject for debouncing textbox changes
  private textboxChangeSubject = new Subject<{value: string, dataItem: any, column: any}>();
  private destroy$ = new Subject<void>();

  constructor(
    private dashboardTrackerService: DashboardTrackerService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.loadDashboardTableData(this.state);
    this.setupTextboxDebounce();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupTextboxDebounce(): void {
    this.textboxChangeSubject
      .pipe(
        debounceTime(2000), // 2 second debounce
        takeUntil(this.destroy$)
      )
      .subscribe(({value, dataItem, column}) => {
        this.updateCellValue(dataItem, column, value);
      });
  }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    this.dashboardTrackerService.getDashboardTableData(filter).subscribe((response) => {
      if (response && response.data && response.columns) {
        this.gridColumns = response.columns;
        this.totalRecords = response.totalRecords || 0;
        this.view = of<GridDataResult>({
          data: response.data,
          total: this.totalRecords
        });        
      } else {
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
      this.isLoading = false;
    });
  }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

  navigateToDashboardConfig(): void {

  }

  // Handle dropdown value changes
  onDropdownValueChange(value: any, dataItem: any, column: any): void {
    this.updateCellValue(dataItem, column, value?.value || value);
  }

  // Handle textbox value changes with debounce
  onTextboxValueChange(value: string, dataItem: any, column: any): void {
    // Immediately update the UI for better user experience
    dataItem[column.name] = value;

    // Send to debounced subject for actual processing
    this.textboxChangeSubject.next({value, dataItem, column});
  }

  // Update or add cell value to the collection
  private updateCellValue(dataItem: any, column: any, newValue: any): void {
    const portfolioCompanyId = dataItem['PCID'];
    const fundId = dataItem['FundID'];
    const columnId = column.id;
    const timeSeriesId = column.timeSeriesId || null;

    // Validate required fields
    if (!portfolioCompanyId || !columnId) {
      this.toastrService.error("Missing required IDs for cell value update.", '', { positionClass: 'toast-center-center' });
      return;
    }

    // Find existing entry or create new one
    const existingIndex = this.cellValueChanges.findIndex(
      item => item.PortfolioCompanyId === portfolioCompanyId &&
              item.FundId === fundId &&
              item.ColumnId === columnId &&
              item.TimeSeriesID === timeSeriesId
    );

    const cellValueDto: DashboardCellValueDto = {
      PortfolioCompanyId: portfolioCompanyId,
      FundId: fundId,
      ColumnId: columnId,
      TimeSeriesID: timeSeriesId,
      CellValue: newValue?.toString() || ''
    };

    if (existingIndex >= 0) {
      // Update existing entry
      this.cellValueChanges[existingIndex] = cellValueDto;
    } else {
      // Add new entry
      this.cellValueChanges.push(cellValueDto);
    }

    // Update the grid data item for immediate UI feedback
    dataItem[column.name] = newValue;

    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
  }

  // Save all cell value changes
  saveCellValues(): void {
    if (this.cellValueChanges.length === 0) {
      return;
    }

    const payload: SaveDashboardCellValuesDto = {
      CellValues: this.cellValueChanges
    };

    this.dashboardTrackerService.saveDashboardCellValues(payload).subscribe({
      next: (response) => {
        console.log('Cell values saved successfully', response);
        // Clear the changes collection after successful save
        this.cellValueChanges = [];
        // Emit change event to parent component
        this.cellChangesUpdated.emit(this.cellValueChanges.length);
      },
      error: (error) => {
        console.error('Error saving cell values', error);
      }
    });
  }

  // Get pending changes count for UI display
  getPendingChangesCount(): number {
    return this.cellValueChanges.length;
  }

  // Clear all pending changes
  clearPendingChanges(): void {
    this.cellValueChanges = [];
    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
    console.log('All pending changes cleared');
  }

  // Get current changes for debugging
  getCurrentChanges(): DashboardCellValueDto[] {
    return [...this.cellValueChanges];
  }
}
