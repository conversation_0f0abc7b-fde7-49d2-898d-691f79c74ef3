import {
  AfterViewInit,
  OnInit,
  Component,
  ElementRef,
  ViewChild,
  Inject,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  SimpleChanges,
} from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConfirmationService } from "primeng/api";
import { DataAnalyticsService } from "src/app/services/data-analytics.service";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import {
  PermissionService,
  KPIModulesEnum,
  FeaturesEnum,
} from "src/app/services/permission.service";
import { DataAnalyticsConstants } from "src/app/common/constants";
import {
  InvestorSections,
  MiscellaneousService,
  InvestorSectionsIds
} from "src/app/services/miscellaneous.service";
import { DatePipe } from "@angular/common";
import { generateSecureId } from "src/app/utils/utils";
declare let $: any;
@Component({
  selector: "app-data-analytics",
  templateUrl: "./data-analytics.component.html",
  styleUrls: ["./data-analytics.component.scss"],
})
export class DataAnalyticsComponent implements OnInit, AfterViewInit {
  feature: typeof FeaturesEnum = FeaturesEnum;
  @ViewChild("revealView", { static: false }) el!: ElementRef;
  private revealView: any;
  collapsed = true;
  myAppUrl: string = "";
  model: any = {};
  excelFile: File;
  isLoader: boolean = false;
  accessToken: string = null;
  analyticsList = [];
  width: number = 0;
  analyticsName: string = null;
  userDetails = "User";
  DashboardId = "DashboardId";
  userPermissions: any = [];
  currentModelRef: any;
  displayModal = false;
  sideNavBaseClass: string;
  @Input() dataAnalyticsModel: any;
  @Input() dataAnalyticsUploadModel: any;
  @Input() triggerApplyEvent: boolean = false;
  defaultSelectedTab: any;
  @Output() onSaveDashboardEvent = new EventEmitter<any>();
  @ViewChild("panel", { read: ElementRef }) public panel: ElementRef<any>;
  @Input() filterAnalyticsList: any;
  enableRight: boolean = false;
  enableLeft: boolean = false;
  showLeftScroll: boolean = false;
  showRightScroll: boolean = true;
  userId: any;
  enabledtab: boolean = false;
  currentTabId: any = 0;
  @Input() tabType: null;
  revealSource: any;
  revealArgs: any;
  description: string = null;
  isPersonal: boolean = true;
  isSave: boolean = false;
  @Input() isCreateDashboard: boolean = false;
  @Input() isFileUpload: boolean = false;
  dealStaticList: any = [];
  fundStaticList: any = [];
  investorStaticList: any = [];
  fixedStaticList: any = [];
  deletePopub: boolean = false;
  deleteModalBody: string;
  primaryDeleteButtonName: string;
  secondaryDeleteButtonName: string;
  deleteModalTitle: string;
  deleteButtonEnabled: boolean = false;
  constructor(
    private datePipe: DatePipe,
    private miscService: MiscellaneousService,
    private identityService: OidcAuthService,
    @Inject("BASE_URL") baseUrl: string,
    private authService: OidcAuthService,
    private dataAnalyticService: DataAnalyticsService,
    private confirmationService: ConfirmationService,
    private permission: PermissionService,
    private alertService: ToastrService,
    protected changeDetectorRef: ChangeDetectorRef
  ) {
    this.model = this.dataAnalyticsModel;
    let config = this.identityService.getEnvironmentConfig();
    if (
      document.location.hostname.toLowerCase() != "localhost" &&
      config.redirect_uri != ""
    ) {
      this.myAppUrl = config.redirect_uri.split("/app")[0] + "/services";
    } else {
      this.myAppUrl = "https://localhost:5001";
    }
    localStorage.setItem("headerName", "Data Analytics");
    this.userPermissions = JSON.parse(permission.getUserPermission());
    if (this.userPermissions.length > 0) {
      this.userId = this.userPermissions[0].userID;
    }
  }
  ngOnInit(): void {
    this.deleteModalBody = DataAnalyticsConstants.deleteModalBody;
    this.primaryDeleteButtonName = DataAnalyticsConstants.primaryDeleteButtonName;
    this.secondaryDeleteButtonName = DataAnalyticsConstants.secondaryDeleteButtonName;
    this.deleteModalTitle = DataAnalyticsConstants.deleteModalTitle;
    this.getDataAnalytics();
    this.setPersonal();
  }
  setPersonal() {
    if (this.tabType == DataAnalyticsConstants.MyDashboardTabName) this.isPersonal = true;
    else this.isPersonal = false;
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes[DataAnalyticsConstants.isCreateDashboard] && this.isCreateDashboard) {
      this.filterAnalyticsList = [];
      this.defaultSelectedTab = null;
      this.loadDashBoard(null);
    }
    if (changes[DataAnalyticsConstants.tabType]) {
      this.isCreateDashboard = false;
      this.defaultSelectedTab = null;
      this.getDataAnalytics();
      this.setPersonal();
      this.loadDashBoard(this.defaultSelectedTab?.dataSetName);
    }
    if (changes["triggerApplyEvent"]) {
    this.getDataSources();
      this.getStaticFieldConfiguration();
      this.loadDashBoard(this.defaultSelectedTab?.dataSetName);
      this.getFixedStaticFieldConfiguration();
    }
    if (
      this.model == undefined &&
      (this.triggerApplyEvent || changes[DataAnalyticsConstants.dataAnalyticsUploadModel])
    ) {
      this.model = this.dataAnalyticsModel;
      this.getDataSources();
    }
    if (
      (this.triggerApplyEvent && this.model !== this.dataAnalyticsModel) ||
      (this.dataAnalyticsUploadModel &&
        this.dataAnalyticsUploadModel?.length > 0)
    ) {
      this.model = this.dataAnalyticsModel;
      this.getDataSources();
    }
  }
  getDataAnalytics = () => {
    this.isLoader = true;
    this.enabledtab = false;
    this.dataAnalyticService
      .getDataAnalyticsList(this.tabType == DataAnalyticsConstants.CommonTabName? false : true)
      .subscribe((response) => {
        if (response?.length > 0) {
          this.analyticsList = response;
          this.filterAnalyticsList = [];
          if (this.analyticsList.length > 0) {
            this.filterAnalyticsList = this.analyticsList;
            this.enabledtab = true;
            if (this.filterAnalyticsList.length > 0) {
              this.filterAnalyticsListFunction();
            }
          }
          this.isLoader = false;
        } else {
          this.isLoader = false;
          this.analyticsList = [];
        this.filterAnalyticsList = [];
          this.defaultSelectedTab = null;
          this.dataAnalyticsUploadModel = [];
          this.loadDashBoard(null);
        }
      });
  };
  isExistDashboard() {
    this.dataAnalyticService.isExistDashboard(this.analyticsName).subscribe({
      next: (response) => {
        if (response) {
          this.alertService.error(
            "A dashboard with name: " + this.analyticsName + " already exists.",
            "",
            {
              positionClass: "toast-center-center",
            }
          );
          this.isLoader = false;
        } else {
          this.displayModal = false;
          this.serializeDashboard(this.revealSource, this.revealArgs, false);
        }
      },
      error: (error) => {
        this.alertService.error(DataAnalyticsConstants.SomethingWentWrong, "", {
          positionClass: "toast-center-center",
        });
      },
    });
  }
  isExistDashboardType(args: any, isOverride: boolean) {
    this.dataAnalyticService.isExistDashboard(this.analyticsName).subscribe({
      next: (response) => {
        if (response) {
          this.alertService.error(
            "A dashboard with name: " + this.analyticsName + " already exists.",
            "",
            {
              positionClass: "toast-center-center",
            }
          );
          this.isLoader = false;
        } else {
          this.saveDashboardByName(args, isOverride);
        }
      },
      error: (error) => {
        this.alertService.error(DataAnalyticsConstants.SomethingWentWrong, "", {
          positionClass: "toast-center-center",
        });
        this.isLoader = false;
      },
    });
  }
  filterAnalyticsListFunction() {
    this.filterAnalyticsList.forEach((element: { isActive: boolean; }) => {
      element.isActive = false;
    });
    let currentTab = this.filterAnalyticsList.filter(
      (x: { id: any; }) => x.id == this.currentTabId
    );
    if (currentTab.length > 0) {
      this.defaultSelectedTab = currentTab[0];
      this.defaultSelectedTab.isActive = true;
    } else {
      this.defaultSelectedTab = this.filterAnalyticsList[0];
      this.defaultSelectedTab.isActive = true;
    }
    this.loadDashBoard(this.defaultSelectedTab.dataSetName);
  }
  loadDashBoard = (dataSetName: any) => {
    this.isLoader = true;
    if(this.revealView != undefined && this.revealView != null)
      this.revealView.showEditDataSource = true;
    if (dataSetName != undefined && dataSetName != null) {
      this.setExportSaveAsOptions();
      if (this.triggerApplyEvent && !this.isSave) {
        this.setEditMode();
      } else {
        this.revealView.canEdit = true;
        this.disableEditMode();
      }
      this.setDashboard(dataSetName);
    } else {
      this.isLoader = false;
      this.setExportSaveAsOptions();
      if (this.revealView != undefined && this.revealView != null) {
        this.revealView.dashboard = new $.ig.RVDashboard();
        if (this.triggerApplyEvent) {
          this.setEditMode();
        } else {
          this.disableEditMode();
        }
      }
    }
  };
  disableEditMode() {
    this.setEditModeByUserId();
    this.revealView.startInEditMode = false;
    if (this.triggerApplyEvent) this.revealView.canAddVisualization = true;
    else this.revealView.canAddVisualization = false;
  }
  setEditMode() {
    this.revealView.showEditDataSource = true;
    this.setEditModeByUserId();
    if (this.permission.checkFeaturePermission({featureId:this.tabType == DataAnalyticsConstants.CommonTabName? this.feature.Common: this.feature.MyDashboard,action: DataAnalyticsConstants.edit,})) {
      this.revealView.startInEditMode = true;
      this.revealView.canAddVisualization = true;
    } else {
      this.revealView.startInEditMode = false;
      this.revealView.canAddVisualization = false;
    }
  }
  setDashboard(dataSetName: any) {
    $.ig.RVDashboard.loadDashboard(dataSetName)
    .then((dashboard) => {
      this.revealView.dashboard = dashboard;
      this.isLoader = false;
    })
    .catch((error) => {
      this.isLoader = false;
      this.revealView.dashboard = null;
    });
  }
  reset() {
    this.analyticsName = null;
    this.description = null;
    this.isPersonal = false;
  }
  save() {
    this.isExistDashboard();
  }
  ngAfterViewInit(): void {
    this.loadDashboardByTabs();
  }
  setExportSaveAsOptions() {
    this.setDeleteButtonEnabled();
    if (!this.revealView) return;
    this.revealView.onMenuOpening = (visualization, args) => {
      const featureId =
        this.tabType === DataAnalyticsConstants.CommonTabName? this.feature.Common : this.feature.MyDashboard;
      const exportPermissionCheck = this.permission.checkFeaturePermission({
        featureId,
        action: "export",
      });
      const saveAsPermissionCheck = this.permission.checkFeaturePermission({
        featureId,
        action: "edit",
      });
      args?.menuItems.forEach((item) => {
        if ((item.title === "Export" || item.title === "Export Image" || item.title === "Export Excel") && !exportPermissionCheck) {
          item.isHidden = true;
        }
        if (( item.title === "Save As" || item.title === "Copy") && !saveAsPermissionCheck) {
          item.isHidden = true;
        }
      });
    };
  }
  setDeleteButtonEnabled() {
    this.deleteButtonEnabled = false;
    if (this.permission.checkFeaturePermission({featureId:this.tabType == DataAnalyticsConstants.CommonTabName? this.feature.Common: this.feature.MyDashboard,action: DataAnalyticsConstants.edit})) {
      this.deleteButtonEnabled = true;
    } 
  }
  /**
   * Clones the current theme settings and customizes the chart colors.
   *
   * This method creates a deep copy of the current theme settings using the `$.ig.RevealSdkSettings.theme.clone()` method.
   * It then customizes the `chartColors` property with a predefined set of color arrays, categorized by different themes such as:
   * - Primary
   * - Secondary
   * - Neutral Gray
   * - Positive
   * - Negative
   * - Noticeable
   * - Informational
   * - Deep Blue
   * - Clear Blue
   * - Teal
   * - Turquoise
   * - Soft Green
   * - Salad Green
   * - Pale Yellow
   * - Pastel Orange
   * - Brand Gradient
   * - Yellow Gradient
   * - Teal Gradient
   * - Blue Gradient
   * - Orange Gradient
   *
   * @returns {object} A cloned theme object with customized chart colors.
   */
  cloneCurrentTheme() {
    let theme = $.ig.RevealSdkSettings.theme.clone();
    theme.chartColors = ["rgba(90, 140, 201, 1)","rgba(156, 204, 100, 1)","rgba(255, 138, 101, 1)","rgba(169, 133, 233, 1)","rgba(255, 183, 77, 1)","rgba(240, 98, 146, 1)","rgba(100, 181, 247, 1)","rgba(161, 136, 127, 1)","rgba(134, 208, 181, 1)","rgba(222, 211, 199, 1)", //Default reveal bi chart color
                          "#00072E", "#021155", "#152B7A", "#4061C7", "#5D7BC7", "#93B0ED", "#C4D9FF", "#CFE0FF", "#E3EEFF", "#EBF3FF",//Primary
                          "#3A003D", "#9C27B0", "#BD00D6", "#E283FC", "#EAABFF", "#F9EBFF",//Secondary
                          "#000000", "#1A1A1A", "#333333", "#4D4D4D",  "#666666", "#B3B3B3", "#CCCCCC", "#E6E6E6","#F2F2F2","#FAFAFA","##FFFFFF",//Neutral Gray
                          "#003B15", "#056122", "#1B873A", "#6AD481", "#A2FAB2", "#CCFFD4", "#EDFFF0", //Positive
                          "#910000", "#B80D10", "#DE3139", "#FF5E6C", "#F0808D", "#FFABB7", "#FFD1DA", "#FFEDF0",//Negative
                          "#CF6700", "#F68523", "#FF984A", "#FFBD96", "#FFD3BD", "#FFEBE3",//Noticeable
                          "#0045D9", "#1A6AFF", "#4089FF", "#8CC0FF", "#B3D7FF", "#D9EDFF", //Informational
                          "#00072E", "#021154", "#152B7A", "#334FA1", "#5D7BC7", "#93B0ED", //Deep Blue
                          "#002C78", "#003F9E", "#0457C4", "#2881EA", "#52A8FF", "#78C0FF", "#9ED5FF", //Clear Blue
                          "#00242B", "#004752", "#006C78", "#13969F", "#35C0C4", "#63EBEB", "#91FFFB",//Teal
                          "#10577D", "#2E7EA3", "#57A9C9", "#8AD6EF", "#BAF0FF", "#E0F9FF",  //Turquoise
                          "#054F39", "#197556", "#389C77", "#63C19C", "#99E8C6", "#CFFFE8",  //Soft Green
                          "#286300", "#438A0C", "#69B02A", "#94D553", "#C6FC88", "#DDFFB0", "#EFFFD6", //Salad Green
                          "#7A7402", "#A1961B", "#C7B740", "#EDDA70", "#FFED9E", "#FFF2C4", "#FFFAEB", //Pale Yellow
                          "#754800", "#9C5B00", "#C2781D", "#E89A46", "#FFB773", "#FFC799", "#FFDABF", //Pastel Orange
                          "#021155", "#9C27B0",  //Brand Gradient
                          "#DEAD09", "#EDCF28",  //Yellow Gradient
                          "#67CCD9", "#A9C97F",  //Teal Gradient
                          "#2F85E9", "#65CBD9",  //Blue Gradient
                          "#EB7600", "#F8BE63"];  //Orange Gradient

    return theme;
}
  loadDashboardByTabs() {
    this.isLoader = true;
    this.revealView = new $.ig.RevealView(this.el.nativeElement);
    var gridConfig = this.revealView.chartTypes.find(
      (x) => x.chartType == "PercentageStackedChart"
    );
    this.revealView.chartTypes.splice(this.revealView.chartTypes.indexOf(gridConfig), 1);
    const url = decodeURIComponent(window.location.href).replace("home", "stacked-percentage-chart");
    this.revealView.chartTypes.push({
      title: "Percentage Bar Stack Chart",
      url: url,
      icon: "assets/dist/images/StackedBarChart.svg",
      groups: ["Custom Visualizations"],
    });
    $.ig.RevealSdkSettings.setBaseUrl(this.myAppUrl);
    $.ig.RevealSdkSettings.enableNewCharts = true;
    $.ig.RevealSdkSettings.serverSideSave = false;
   // $.ig.RevealSdkSettings.theme = this.cloneCurrentTheme();//Color palate
    $.ig.RevealSdkSettings.setAdditionalHeadersProvider((url: string) => {
      let headers: Record<string, any> = {};
      headers["Authorization"] = this.authService.getToken();
      headers[this.userDetails] = this.userPermissions[0].userID;
      return headers;
    });
    this.revealView.showFilters = true;
    this.revealView.singleVisualizationMode = false;
    this.revealView.onFieldsInitializing = function (args: { useCustomSort: boolean; }) {
      args.useCustomSort = true;
    };
    if (
      this.filterAnalyticsList != undefined &&
      this.filterAnalyticsList.length > 0
    ) {
      this.revealView.startInEditMode = true;
      this.revealView.canEdit = true;
      this.revealView.canAddVisualization = true;
      this.defaultSelectedTab = this.filterAnalyticsList[0];
      this.loadDashBoard(this.defaultSelectedTab.dataSetName);
    } else {
      if (this.triggerApplyEvent) {
        this.revealView.startInEditMode = true;
        this.revealView.canEdit = true;
        this.revealView.canAddVisualization = true;
      } else {
        this.revealView.startInEditMode = false;
        this.revealView.canEdit = false;
        this.revealView.canAddVisualization = false;
      }
    }
    this.revealBiOnSave();
    this.isLoader = false;
  }
  setEditModeByUserId() {
    if (this.permission.checkFeaturePermission({featureId:this.tabType == DataAnalyticsConstants.CommonTabName? this.feature.Common: this.feature.MyDashboard,action: DataAnalyticsConstants.edit})) {
      this.revealView.canEdit = true;
      this.deleteButtonEnabled = true;
    } else {
      this.revealView.canEdit = false;
    }
  }
  revealBiOnSave() {
    this.revealView.onSave = (rv: any, args: { saveAs: any; name: string; dashboardId: any; })=>{
      this.revealSource = rv;
      this.revealArgs = args;
      if (
        args.saveAs ||
        this.defaultSelectedTab?.id == undefined ||
        this.defaultSelectedTab == null ||
        this.defaultSelectedTab?.id == null ||
        this.defaultSelectedTab?.id == 0
      ) {
        this.reset();
        this.displayModal = true;
        this.setPersonal();
      } else {
        if (args.name != "" || args.name != null) {
          this.analyticsName = args.name;
        }
        args.dashboardId = args.name;
        this.serializeDashboard(rv, args, false);
      }
    };
  }
  serializeDashboard(rv: any, args: any, isOverride: boolean) {
    this.isLoader = true;
    this.isSave = true;
    let isExist: boolean = false;
    if (args.saveAs || this.defaultSelectedTab?.id == undefined) {
      this.currentTabId = 0;
    } else {
      this.currentTabId = this.defaultSelectedTab?.id;
      this.analyticsName = args.name;
      if (args.name != this.defaultSelectedTab.dataSetName)
        this.isExistDashboardType(args, isOverride);
      else this.saveDashboardByName(args, isOverride);
    }
    if (args.saveAs || this.defaultSelectedTab == null) {
      this.saveDashboardByName(args, isOverride);
    }
  }
  saveDashboardByName(args: any, isOverride: boolean) {
    this.isLoader = true;
    let dashboardId = isOverride
      ? this.defaultSelectedTab?.id
      : this.currentTabId;
    let userId = this.userPermissions[0].userID;
    args.dashboardId = args.name = this.analyticsName;
    args.saveFinished();
    args.serialize((bytes: any) => {
      let dashboard = {
        File: btoa(String.fromCharCode.apply(null, bytes)),
        DashboardId: dashboardId,
        DatasetName: this.analyticsName,
        UserId: userId,
        Description: this.description,
        isPersonal: this.isPersonal,
      };
      this.dataAnalyticService.saveDashboard(dashboard).subscribe({
        next: () => {
          this.reset();
          this.successInfo();
          this.onSaveDashboardEvent.emit(false);
            this.getDataAnalytics();
          this.isLoader = false;
        },
        error: (error) => {
          this.isLoader = false;
        },
      });
    });
  }
  saveDashBoard(args: any, isSaveAs: boolean) {
    if (isSaveAs || this.defaultSelectedTab?.id == undefined) {
      this.currentTabId = 0;
    } else {
      this.currentTabId = this.defaultSelectedTab?.id;
    }
    $.ig.RevealSdkSettings.setAdditionalHeadersProvider((url: string) => {
      let headers: Record<string, any> = {};
      headers["Authorization"] = this.authService.getToken();
      headers[this.userDetails] = this.userPermissions[0].userID;
      headers[this.DashboardId] = this.currentTabId;
      return headers;
    });
    args.name = this.analyticsName;
    args.saveFinished();
    this.successInfo();
    this.isLoader = true;
    setTimeout(() => {
      this.reset();
      this.getDataAnalytics();
      this.isLoader = false;
    }, 4000);
  }
  successInfo() {
    this.analyticsName = null;
    this.alertService.success("Dashboard added successfully", "", {
      positionClass: "toast-center-center",
    });
  }
  getUploadDataSource(file: any) {
    let excelDataSourceItem: { defaultRefreshRate: number; title: any; } ;
    let localFileItem = new $.ig.RVLocalFileDataSourceItem();
    localFileItem.uri = "local:/" + file.newFileName;
    excelDataSourceItem = new $.ig.RVExcelDataSourceItem(localFileItem);
    excelDataSourceItem.defaultRefreshRate = 0;
    excelDataSourceItem.title = file.oldFileName;
    return excelDataSourceItem;
  }
  
  getDataSources() {
    if (this.revealView != undefined)
      this.revealView.onDataSourcesRequested = (callback) => {
        let payloadDataSource = [];
        if (this.dataAnalyticsUploadModel?.length > 0) {
          this.dataAnalyticsUploadModel.forEach((element) => {
            if (element.newFileName != null && element.newFileName != "") 
              payloadDataSource.push(this.getUploadDataSource(element));
          });
        }
        if (this.validMandatoryPortfolioCompany(this.dataAnalyticsModel.portfolioCompanyModel)) {
          payloadDataSource.push(this.getDataSourcePortfolioCompany());
        }
        if (this.validMandatoryDeal(this.dataAnalyticsModel.dealModel)) {
          payloadDataSource.push(this.getDataSourceDeals());
        }
        if (this.validMandatoryFund(this.dataAnalyticsModel.fundModel)) {
          payloadDataSource.push(this.getDataSourceFunds());
        }
        if (this.validMandatoryInvestor(this.dataAnalyticsModel.investorModel)) {
          payloadDataSource.push(this.getDataSourceInvestors());
        }
        if (this.validMandatoryESG(this.dataAnalyticsModel.ESGModel)) {
          payloadDataSource.push(this.getDataSourceESG());
        }
        callback(new $.ig.RevealDataSources([], payloadDataSource, false));
      };
  }
  onResized(event: any) {
    this.width = event?.newRect?.width;
    this.sideNavBaseClass = "dashboardview-width-without-expand";
  }
  getDataSourceDeals() {
    const restDataSource = new $.ig.RVRESTDataSource();
    let idValue =generateSecureId(9);
    restDataSource.id = idValue.toString();
    restDataSource.method = DataAnalyticsConstants.PostMethod;
    restDataSource.defaultRefreshRate = 0;
    restDataSource.title = DataAnalyticsConstants.DealDetails;
    restDataSource.subtitle = DataAnalyticsConstants.SubTitle;
    restDataSource.url =
      this.myAppUrl + DataAnalyticsConstants.getDealDataSourceURL;
    restDataSource.body = JSON.stringify(this.getDealsPayload());
    restDataSource.useAnonymousAuthentication = true;
    return this.getCommonDataSourceItem(
      restDataSource,
      DataAnalyticsConstants.DealDetails,
      DataAnalyticsConstants.SubTitle,
      DataAnalyticsConstants.DealDetails
    );
  }
  getDataSourceFunds() {
    const restDataSource = new $.ig.RVRESTDataSource();
    let idValue =generateSecureId(9);
    restDataSource.id = idValue.toString();
    restDataSource.method = DataAnalyticsConstants.PostMethod;
    restDataSource.defaultRefreshRate = 0;
    restDataSource.title = DataAnalyticsConstants.FundDetails;
    restDataSource.subtitle = DataAnalyticsConstants.SubTitle;
    restDataSource.url =
      this.myAppUrl + DataAnalyticsConstants.getFundsDataSourceURL;
    restDataSource.body = JSON.stringify(this.getFundsPayload());
    restDataSource.useAnonymousAuthentication = true;
    return this.getCommonDataSourceItem(
      restDataSource,
      DataAnalyticsConstants.FundDetails,
      DataAnalyticsConstants.SubTitle,
      DataAnalyticsConstants.FundDetails
    );
  }
  getDataSourceInvestors() {
    const restDataSource = new $.ig.RVRESTDataSource();
    let idValue = generateSecureId(9);
    restDataSource.id = idValue.toString();
    restDataSource.method = DataAnalyticsConstants.PostMethod;
    restDataSource.defaultRefreshRate = 0;
    restDataSource.title = DataAnalyticsConstants.InvestorDetails;
    restDataSource.subtitle = DataAnalyticsConstants.SubTitle;
    restDataSource.url =
      this.myAppUrl + DataAnalyticsConstants.getInvestorsDataSourceURL;
    restDataSource.body = JSON.stringify(this.getInvestorPayload());
    restDataSource.useAnonymousAuthentication = true;
    return this.getCommonDataSourceItem(
      restDataSource,
      DataAnalyticsConstants.InvestorDetails,
      DataAnalyticsConstants.SubTitle,
      DataAnalyticsConstants.InvestorDetails
    );
  }
  getDataSourcePortfolioCompany() {
    const restDataSource = new $.ig.RVRESTDataSource();
    let idValue = generateSecureId(9);
    restDataSource.id = idValue.toString();
    restDataSource.method = DataAnalyticsConstants.PostMethod;
    restDataSource.defaultRefreshRate = 0;
    restDataSource.title = DataAnalyticsConstants.PCDetails;
    restDataSource.subtitle = DataAnalyticsConstants.SubTitle;
    restDataSource.url =
      this.myAppUrl + DataAnalyticsConstants.getPCDetailsDataSourceURL;
    restDataSource.body = JSON.stringify(this.getPCPayload());
    restDataSource.useAnonymousAuthentication = true;
    return this.getCommonDataSourceItem(
      restDataSource,
      DataAnalyticsConstants.PCDetails,
      DataAnalyticsConstants.SubTitle,
      DataAnalyticsConstants.PCDetails
    );
  }
  getDataSourceESG() {
    const restDataSource = new $.ig.RVRESTDataSource();
    let idValue = generateSecureId(9);
    restDataSource.id = idValue.toString();
    restDataSource.method = DataAnalyticsConstants.PostMethod;
    restDataSource.defaultRefreshRate = 0;
    restDataSource.title = DataAnalyticsConstants.ESGDetails;
    restDataSource.subtitle = DataAnalyticsConstants.SubTitle;
    restDataSource.url =
      this.myAppUrl + DataAnalyticsConstants.getESGDetailsDataSourceURL;
    restDataSource.body = JSON.stringify(this.getESGPayload());
    restDataSource.useAnonymousAuthentication = true;
    return this.getCommonDataSourceItem(
      restDataSource,
      DataAnalyticsConstants.ESGDetails,
      DataAnalyticsConstants.SubTitle,
      DataAnalyticsConstants.ESGDetails
    );
  }
  getCommonDataSourceItem(
    restDataSource: any,
    title: string,
    subtitle: string,
    type: string
  ) {
    const restDataSourceItem = new $.ig.RVRESTDataSourceItem(restDataSource);
    restDataSourceItem.id = DataAnalyticsConstants.RestDataSourceItem;
    const jsonDataSourceItem = new $.ig.RVJsonDataSourceItem(
      restDataSourceItem
    );
    jsonDataSourceItem.id = DataAnalyticsConstants.JsonDataSourceItem;
    jsonDataSourceItem.defaultRefreshRate = 0;
    jsonDataSourceItem.title = type;
    jsonDataSourceItem.subtitle = subtitle;
    jsonDataSourceItem.config = this.getCustomPayload(type);
    return jsonDataSourceItem;
  }
  getCustomPayload(type: string) {
    let StaticArray: { iterationDepth: number; columnsConfig: any[]; }
    let result = [];
    if (type == DataAnalyticsConstants.PCDetails) {
      let staticLineItem = this.getPCStaticPayload();
      result.push(...staticLineItem);
    } else if (type == DataAnalyticsConstants.DealDetails) {
      let StaticDealArray = this.getDealStaticPayload();
      result.push(...StaticDealArray);
    } else if (type == DataAnalyticsConstants.FundDetails) {
      let StaticFundArray = this.getFundStaticPayload();
      result.push(...StaticFundArray);
    } else if (type == DataAnalyticsConstants.InvestorDetails) {
      let StaticInvestorArray = this.getInvestorStaticPayload();
      result.push(...StaticInvestorArray);
    }else  if (type == DataAnalyticsConstants.ESGDetails) {
      let staticLineItem = this.getESGStaticPayload();
      result.push(...staticLineItem);
    }
    StaticArray = {
      iterationDepth: 0,
      columnsConfig: result,
    };
    return JSON.stringify(StaticArray);
  }
  getPCStaticPayload() {
    let StaticArray = [
      { key: "Period", type: 0 },
      { key: "PeriodType", type: 0 },
      { key: "PeriodDate", type: 2 },
      { key: "ValueType", type: 0 },
      { key: "KpiType", type: 0 },
      { key: "~", uniqueName: "Level 1", type: 1 },
      { key: "KpiValue", type: 0 },
    ];
    let DefaultArray = [];
    let returnArray = [];
    let isParentChildGraph = this.model?.portfolioCompanyModel.isParentChildGraph;
    this.model.portfolioCompanyModel.kpiItems.forEach((element) => {
      const checkDuplicateKpi = (roleParam) =>
      DefaultArray.some(
        ({ key }) => key.toLowerCase() == element.kpi.toLowerCase()
      );
    if (!checkDuplicateKpi(element.kpi)) {
      if (element.kpiFieldName == null && !element?.isModuleHeader && !isParentChildGraph) {
        DefaultArray.push({ key: element.kpi, type: 1 });
      } else if(!element?.isModuleHeader || isParentChildGraph) {
        DefaultArray.push({ key: element.kpi, type: 0 });
      }
    }
    });
    returnArray.push(...this.GetFixedFields(false,false), ...StaticArray, ...DefaultArray);
    return returnArray;
  }
  getESGStaticPayload() {
    const staticArray = [
      { key: "Period", type: 0 },
      { key: "PeriodType", type: 0 },
      { key: "PeriodDate", type: 2 },
      { key: "ValueType", type: 0 },
      { key: "KpiType", type: 0 },
      { key: "~", uniqueName: "Level 1", type: 1 },
      { key: "KpiValue", type: 0 },
    ];
    const defaultArray = [];
    let isParentChildGraph = this.model?.ESGModel.isParentChildGraph;
    this.model.ESGModel.kpiItems.forEach((element) => {
      const elementKpiLower = element.kpi.toLowerCase();
      const isDuplicateKpi = defaultArray.some(
        ({ key }) => key.toLowerCase() === elementKpiLower
      );
      if (!isDuplicateKpi) {
        if (element.kpiFieldName == null && !element?.isModuleHeader && !isParentChildGraph) {
          defaultArray.push({ key: element.kpi, type: 1 });
        } else if(!element?.isModuleHeader || isParentChildGraph) {
          defaultArray.push({ key: element.kpi, type: 0 });
        }
      }
    });  
    return [...this.GetFixedFields(false, false), ...staticArray, ...defaultArray];
  }
  getSectionId(id: string) {
    if (id == InvestorSections.InvestedFunds) return InvestorSectionsIds.InvestedFunds;
    else if (id == InvestorSections.InvestorFundDetails) return InvestorSectionsIds.InvestorFundDetails;
    else if (id == InvestorSections.CompanyPerformance) return InvestorSectionsIds.CompanyPerformance;
    else if (id == InvestorSections.InvestorCompanyDetails) return InvestorSectionsIds.InvestorCompanyDetails;
    else if (id == InvestorSections.ValuationData) return InvestorSectionsIds.ValuationData;
  }
  getInvestorStaticPayload() {
    let DynamicArray = [];
    let returnArray = [];
    let sectionId;
    let fixedArray = [];
    if (this.investorStaticList != null && this.investorStaticList.length > 0) {
      if (
        this.model?.investorModel?.investorSections?.id &&
        this.model?.investorModel?.investorSections?.id != ""
      )
        sectionId = this.getSectionId(
          this.model?.investorModel?.investorSections?.id
        );
      let investorStaticList = this.investorStaticList?.filter(
        (kpi) => kpi.subPageID == sectionId 
      );
      investorStaticList?.forEach((element1) => {
        if (DynamicArray.length > 1 && element1.name !=  DataAnalyticsConstants.InvestorName && element1.name !=  DataAnalyticsConstants.FundName
          && element1.name !=  DataAnalyticsConstants.InvestorId && element1.name !=  DataAnalyticsConstants.FundId && !element1?.isModuleHeader) {
          const checkDuplicateKpi = (roleParam) =>
            DynamicArray.some(
              ({ key }) => key.toLowerCase() == element1.aliasName.toLowerCase()
            );
          if (!checkDuplicateKpi(element1.aliasName)) {
            DynamicArray.push({ key: element1.aliasName, type: 0 });
          }
        } else if(element1.name != DataAnalyticsConstants.InvestorName && element1.name !=  DataAnalyticsConstants.FundName
          && element1.name !=  DataAnalyticsConstants.InvestorId && element1.name !=  DataAnalyticsConstants.FundId && !element1?.isModuleHeader) {
          DynamicArray.push({ key: element1.aliasName, type: 0 });
        }
      });
    }
    let StaticArray = [
      { key: "Period", type: 0 },
      { key: "PeriodDate", type: 2 },
    ];
    let levelArray = [{ key: "~", uniqueName: "Level 1" }];
    fixedArray = this.GetFixedFields(true,false);
    if(sectionId != InvestorSectionsIds.CompanyPerformance && sectionId != InvestorSectionsIds.InvestorCompanyDetails){
      fixedArray = fixedArray.filter(x => x.name != DataAnalyticsConstants.CompanyName);
    }
    returnArray.push(...fixedArray,...StaticArray,...levelArray,...DynamicArray);
    return returnArray;
  }
  getDealStaticPayload() {
    let returnArray = [];
    let DefaultArray =[];
    let ValuesArray = [];
    let DateArray = [];
     this.model.dealModel.kpiItems.sort((a, b) => a.moduleId - b.moduleId);
      this.model.dealModel.kpiItems.forEach(element => {
        let  checkDuplicateKpi = (roleParam: string) => DefaultArray.some(({ key }) => key.toLowerCase() == roleParam.toLowerCase());
        let checkDuplicateValues = (roleParam: string) => ValuesArray.some(({ key }) => key.toLowerCase() == roleParam.toLowerCase());
        let checkDuplicateDate = (roleParam: string) => DateArray.some(({ key }) => key.toLowerCase() == roleParam.toLowerCase());
        if (!checkDuplicateKpi(element.kpi) && !checkDuplicateValues(element.kpi) && !checkDuplicateDate(element.kpi)) {
          if(element.type == null && !element?.isModuleHeader){
            DefaultArray.push({ key: element.kpi,type:0 });
          }else if(!element?.isModuleHeader){
            if(element.type == 1)
              ValuesArray.push({ key: element.kpi,type:element.type});
            else if(element.type == 2)
            DateArray.push({ key: element.kpi, type: element.type });
          else
            DefaultArray.push({ key: element.kpi, type: element.type });
        }
      }
      });
    let StaticArray = [{ key: "Period", type: 0 }, { key: "PeriodType", type: 0 }, { key: "PeriodDate", type: 2 }, { key: "~", uniqueName: "Level 1", type: 1 }];
    returnArray.push(...this.GetFixedFields(false,false), ...StaticArray, ...ValuesArray, ...DateArray, ...DefaultArray);
    return returnArray;
  }

  GetFixedFields(isInvestorTab:boolean, isFundTab:boolean){
    let DynamicArray = [];
    let fixedStaticListCopy = [];
    if(isFundTab){
      fixedStaticListCopy =this.fixedStaticList.filter(x => x.name == DataAnalyticsConstants.FundName);
    } else if(!isInvestorTab){
      fixedStaticListCopy =this.fixedStaticList.filter(x => x.name != DataAnalyticsConstants.InvestorName);
    } else{
      fixedStaticListCopy = this.fixedStaticList;
    }
    fixedStaticListCopy.forEach(x => {
      DynamicArray.push({ key: x.aliasName, type: 0,name:x.name})
    })
    return DynamicArray;
  }
  getFundStaticPayload() {
    let DynamicArray = [];
    let returnArray = [];
    if (this.dataAnalyticsModel?.fundModel?.kpiItems != null && this.dataAnalyticsModel?.fundModel?.kpiItems.length > 0) {
      this.dataAnalyticsModel?.fundModel.kpiItems.forEach((element1: { kpi: string;kpiFieldName: string; isModuleHeader:boolean }) => {
        if (DynamicArray.length > 1 && element1.kpiFieldName != DataAnalyticsConstants.FundName && !element1?.isModuleHeader) {
          const checkDuplicateKpi = (roleParam) =>
            DynamicArray.some(
              ({ key }) => key.toLowerCase() == element1.kpi.toLowerCase()
            );
          if (!checkDuplicateKpi(element1.kpi)) {
            DynamicArray.push({ key: element1.kpi, type: 0 });
          }
        } else if(element1.kpiFieldName != DataAnalyticsConstants.FundName && !element1?.isModuleHeader) {
          DynamicArray.push({ key: element1.kpi, type: 0 });
        }
      });
    }
    let StaticArray = [
      { key: "Period", type: 0 },
      { key: "PeriodDate", type: 2 },
    ];
    let levelArray = [{ key: "~", uniqueName: "Level 1", type: 1 }];
    returnArray.push(...this.GetFixedFields(false,true),...StaticArray, ...levelArray, ...DynamicArray);
    return returnArray;
  }
  getStaticFieldConfiguration() {
    this.isLoader = true;
    this.dataAnalyticService.getStaticFields().subscribe({
      next: (result: any) => {
        if (result != null && result != undefined) {
          this.parseJsonResponse(result);
        }
        this.isLoader = false;
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  getFixedStaticFieldConfiguration() {
    this.isLoader = true;
    this.dataAnalyticService.getFixedStaticFields().subscribe({
      next: (result: any) => {
        if (result != null && result != undefined) {
          this.fixedStaticList = result.fixedStaticFields;
        }
        this.isLoader = false;
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }

  parseJsonResponse(result: any) {
    this.dealStaticList = result.dealStaticFields;
    this.fundStaticList = result.fundStaticFields;
    this.investorStaticList = result.investorStaticFields;
  }
  getPCPayload() {
    let startDate = new Date(this.model?.portfolioCompanyModel.period[0]);
    let endDate = new Date(this.model?.portfolioCompanyModel.period[1]);
    let companyId = this.model?.portfolioCompanyModel.companyList.map(function (
      company
    ) {
      return company.companyId;
    });
    let moduleId = this.model?.portfolioCompanyModel.moduleList
      .filter((module) => !module.staticSection)
      .map(function (type) {
        return type.moduleId;
      });
    let subPageIds = this.model?.portfolioCompanyModel.moduleList
      .filter((module) => module.staticSection)
      .map(function (type) {
        return type.moduleId;
      });
    let objectKpiLineItems = this.model?.portfolioCompanyModel.kpiItems.filter(item => !item.isModuleHeader);
    const pageConfigFields = objectKpiLineItems
      .filter((x) => subPageIds.includes(x.moduleId))
      .map((kpi) => ({ SubPageId: kpi.moduleId, FieldId: kpi.kpiId }));
    return {
      CompanyId: companyId,
      ModuleIds: moduleId,
      StartDate: this.datePipe
        .transform(startDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      EndDate: this.datePipe
        .transform(endDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      KpiIds: [],
      ToCurrencyId:
        this.model?.portfolioCompanyModel.currenceList?.currencyID == undefined
          ? 0
          : this.model?.portfolioCompanyModel.currenceList?.currencyID,
      CurrencyRateSource:
        this.model?.portfolioCompanyModel.fxRates?.type == undefined
          ? ""
          : this.model?.portfolioCompanyModel.fxRates?.type,
      IsAnalytics: true,
      IsParentChildGraph : this.model?.portfolioCompanyModel.isParentChildGraph,
      DataAnalyticsKpiFilter: {
        CompanyKpiIds: this.getKpiIdsByModule(KPIModulesEnum.Company,objectKpiLineItems),
        InvestmentKpiIds: this.getKpiIdsByModule(KPIModulesEnum.Investment,objectKpiLineItems),
        OperationalKpiIds: this.getKpiIdsByModule(KPIModulesEnum.Operational,objectKpiLineItems),
        TradingKpiIds: this.getKpiIdsByModule(KPIModulesEnum.TradingRecords,objectKpiLineItems),
        CreditKpiIds: this.getKpiIdsByModule(KPIModulesEnum.CreditKPI,objectKpiLineItems),
        ProfitLossKpiIds: this.getKpiIdsByModule(KPIModulesEnum.ProfitAndLoss,objectKpiLineItems),
        BalanceSheetKpiIds: this.getKpiIdsByModule(KPIModulesEnum.BalanceSheet,objectKpiLineItems),
        CashflowKpiIds: this.getKpiIdsByModule(KPIModulesEnum.CashFlow,objectKpiLineItems),
        ImpactKpiIds: this.getKpiIdsByModule(KPIModulesEnum.Impact,objectKpiLineItems),
        CapTable1KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable1,objectKpiLineItems),
        CapTable2KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable2,objectKpiLineItems),
        CapTable3KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable3,objectKpiLineItems),
        CapTable4KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable4,objectKpiLineItems),
        CapTable5KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable5,objectKpiLineItems),
        CapTable6KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable6,objectKpiLineItems),
        CapTable7KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable7,objectKpiLineItems),
        CapTable8KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable8,objectKpiLineItems),
        CapTable9KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable9,objectKpiLineItems),
        CapTable10KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CapTable10,objectKpiLineItems),
        CustomTable1KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CustomTable1, objectKpiLineItems),
        CustomTable2KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CustomTable2, objectKpiLineItems),
        CustomTable3KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CustomTable3, objectKpiLineItems),
        CustomTable4KpiIds: this.getKpiIdsByModule(KPIModulesEnum.CustomTable4, objectKpiLineItems),
        OtherKPI1KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI1, objectKpiLineItems),
        OtherKPI2KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI2, objectKpiLineItems),
        OtherKPI3KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI3, objectKpiLineItems),
        OtherKPI4KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI4, objectKpiLineItems),
        OtherKPI5KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI5, objectKpiLineItems),
        OtherKPI6KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI6, objectKpiLineItems),
        OtherKPI7KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI7, objectKpiLineItems),
        OtherKPI8KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI8, objectKpiLineItems),
        OtherKPI9KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI9, objectKpiLineItems),
        OtherKPI10KpiIds: this.getKpiIdsByModule(KPIModulesEnum.OtherKPI10, objectKpiLineItems),
        OtherCapTable1: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable1, objectKpiLineItems),
        OtherCapTable2: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable2, objectKpiLineItems),
        OtherCapTable3: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable3, objectKpiLineItems),
        OtherCapTable4: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable4, objectKpiLineItems),
        OtherCapTable5: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable5, objectKpiLineItems),
        OtherCapTable6: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable6, objectKpiLineItems),
        OtherCapTable7: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable7, objectKpiLineItems),
        OtherCapTable8: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable8, objectKpiLineItems),
        OtherCapTable9: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable9, objectKpiLineItems),
        OtherCapTable10: this.getKpiIdsByModule(KPIModulesEnum.OtherCapTable10, objectKpiLineItems),
        PageConfigFields: pageConfigFields,
      },
    };
  }
  getKpiIdsByModule = (moduleId, objectKpiLineItems) => objectKpiLineItems
    .filter(x => x.moduleId === moduleId)
    .map(kpi => kpi.kpiId);

  getESGPayload() {
    const { period, companyList, moduleList, kpiItems } = this.model?.ESGModel ?? {};
    const startDate = new Date(period?.[0]);
    const endDate = new Date(period?.[1]);
    const companyId = companyList?.map((company) => company.companyId) ?? [];
    const selectedModules = moduleList
      ?.filter((module) => !module.staticSection)
      .map((type) => ({ Name: type.field, SubPageId: type.subPageId, FieldId: 0 })) ?? [];
    const filterKpiLineItems = kpiItems?.filter(item => !item.isModuleHeader);
    const pageConfigFields = filterKpiLineItems?.map((kpi) => ({ SubPageId: kpi.moduleId, FieldId: kpi.kpiId, Name: kpi.kpi })) ?? [];
  
    return {
      CompanyIds: companyId,
      EsgModules: selectedModules,
      StartDate: `${this.datePipe.transform(startDate, DataAnalyticsConstants.StaticDateFormat)}`,
      EndDate: `${this.datePipe.transform(endDate, DataAnalyticsConstants.StaticDateFormat)}`,
      ConfigFields: pageConfigFields,
      Combine: true,
      Sections: [],
      FundIds: [],
      IsParentChildGraph : this.model?.ESGModel?.isParentChildGraph,
    };
  }
  convertTransformDate(date) {
    return this.datePipe.transform(
      date,
      DataAnalyticsConstants.StaticDateFormat
    );
  }
  getDealsPayload() {
    let companyId = this.model?.dealModel.companyList.map(function (company: { companyId: any; }) {
      return company.companyId;
    });
    let SubPageIds =  [...new Set(this.model?.dealModel.moduleList.map(function (SubPageId: { moduleId: any; }) {
      return SubPageId.moduleId;
    }))];
    let kpiItems = this.model?.dealModel.kpiItems.filter(item => !item.isModuleHeader);
    let FieldIds = kpiItems?.map(function (FieldIds: { kpiId: any; }) {
      return FieldIds.kpiId;
    });
    let startDate = new Date(this.model?.dealModel.period[0]);
    let endDate = new Date(this.model?.dealModel.period[1]);
    return {
      Sections: [
        DataAnalyticsConstants.BasicDetails,
        DataAnalyticsConstants.PCFundHoldingDetails,
        DataAnalyticsConstants.StaticInformation,
        DataAnalyticsConstants.FundStaticInformation
      ],
      DealId: [],
      DealCombine: true,
      IsExcelPlugin:false,
      CompanyIds: companyId,
      StartDate: this.datePipe
        .transform(startDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      EndDate: this.datePipe
        .transform(endDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
        SubPageIds:SubPageIds,
        FieldIds:FieldIds,
    };
  }
  getInvestorPayload() {
    let startDate = new Date(this.model?.investorModel.period[0]);
    let endDate = new Date(this.model?.investorModel.period[1]);
    let companyId = this.model?.investorModel.companyList.map(function (
      company: { companyId: any; }
    ) {
      return company.companyId;
    });
    let investorIds = this.model?.investorModel.investorList.map(function (
      investor: { investorId: any; }
    ) {
      return investor.investorId;
    });
    let fundIds = this.model?.investorModel.fundList.map(function (fund: { fundID: any; }) {
      return fund.fundID;
    });
    return {
      Sections: [
        this.model?.investorModel.investorSections?.type == undefined
          ? ""
          : this.model?.investorModel.investorSections?.type,
      ],
      CompanyIds: companyId,
      InvestorIds: investorIds,
      StartDate: this.datePipe
        .transform(startDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      EndDate: this.datePipe
        .transform(endDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      Combine: true,
      FundIds: fundIds,
    };
  }
  getFundsPayload() {
    let fundID = this.model?.fundModel.fundList.map(function (fund: { fundID: any; }) {
      return fund.fundID;
    });
    let SubPageIds =  [...new Set(this.model?.fundModel.moduleList.map(function (SubPageId: { moduleId: any; }) {
      return SubPageId.moduleId;
    }))];
    let kpiItems = this.model?.fundModel.kpiItems.filter(item => !item.isModuleHeader);
    let FieldIds = kpiItems?.map(function (FieldIds: { kpiId: any; }) {
      return FieldIds.kpiId;
    });
    let startDate = new Date(this.model?.fundModel.period[0]);
    let endDate = new Date(this.model?.fundModel.period[1]);
    return {
      sections: [
        DataAnalyticsConstants.StaticFundDetails,
        DataAnalyticsConstants.FundTerms,
        DataAnalyticsConstants.GeographicLocations,
        DataAnalyticsConstants.TrackRecord,
      ],
      FundId: fundID,
      IsCombine: true,
      StartDate: this.datePipe
        .transform(startDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
      EndDate: this.datePipe
        .transform(endDate, DataAnalyticsConstants.StaticDateFormat)
        .toString(),
        SubPageIds:SubPageIds,
        FieldIds:FieldIds,
        IsFund:true
    };
  }
  changeTabType(tab: any) {
    this.defaultSelectedTab = tab;
    this?.filterAnalyticsList?.forEach((row: { isActive: boolean; }) => (row.isActive = false));
    tab.isActive = true;
    if (
      this.defaultSelectedTab != undefined &&
      this.defaultSelectedTab != null
    ) {
      this.loadDashBoard(this.defaultSelectedTab.dataSetName);
    }
  }
  moveRight() {
    let scroll = 300;
    let remainingScroll =
      this.panel.nativeElement.scrollWidth -
      (this.panel.nativeElement.offsetWidth +
        this.panel.nativeElement.scrollLeft);
    if (remainingScroll > 2) {
      this.enableLeft = false;
      this.enableRight = true;
    } else {
      this.enableLeft = true;
      this.enableRight = false;
    }
    if (remainingScroll > scroll && remainingScroll < scroll * 2) {
      scroll = remainingScroll;
    }
    try {
      this.panel.nativeElement.scrollTo({
        left: this.panel.nativeElement.scrollLeft + scroll,
        top: 0,
        behavior: "smooth",
      });
    } catch {
      this.panel.nativeElement.scrollLeft =
        this.panel.nativeElement.scrollLeft + scroll;
    }
    setTimeout(
      function (local: any) {
        local.setScrollButtonVisibility();
      },
      500,
      this
    );
  }
  rightScrollTimer: any;
  leftScrollTimer: any;
  startLeftScrolling() {
    this.leftScrollTimer = setInterval(
      function (local: any) {
        local.moveLeft();
      },
      500,
      this
    );
  }
  startRightScrolling() {
    this.rightScrollTimer = setInterval(
      function (local: any) {
        local.moveRight();
      },
      500,
      this
    );
  }
  propagateChange = (_: any) => {};
  stopLeftScrolling() {
    clearInterval(this.leftScrollTimer);
  }
  stopRightScrolling() {
    clearInterval(this.rightScrollTimer);
  }

  moveLeft() {
    if (this.panel.nativeElement.scrollLeft > 1) {
      this.enableRight = false;
      this.enableLeft = true;
    } else {
      this.enableRight = true;
      this.enableLeft = false;
    }
    try {
      this.panel.nativeElement.scrollLeft.scrollTo({
        left: this.panel.nativeElement.scrollLeft - 300,
        top: 0,
        behavior: "smooth",
      });
    } catch {
      this.panel.nativeElement.scrollLeft =
        this.panel.nativeElement.scrollLeft - 300;
    }

    setTimeout(
      function (local: any) {
        local.setScrollButtonVisibility();
      },
      500,
      this
    );
  }
  setScrollButtonVisibility() {
    this.changeDetectorRef.detectChanges();
    if (
      this.panel.nativeElement.offsetWidth +
        this.panel.nativeElement.scrollLeft ==
      this.panel.nativeElement.scrollWidth
    ) {
      this.stopRightScrolling();
      this.showRightScroll = false;
    } else {
      this.showRightScroll = true;
    }
    if (this.panel.nativeElement.scrollLeft == 0) {
      this.stopLeftScrolling();
      this.showLeftScroll = false;
    } else {
      this.showLeftScroll = true;
    }
  }
  validMandatoryInvestor(investorModel: any) {
    return investorModel?.companyList?.length > 0 &&
           investorModel?.fundList?.length > 0 &&
           investorModel?.investorList?.length > 0 &&
           investorModel?.investorSections?.id != null &&
           investorModel?.period?.length > 0;
  }
  validMandatoryPortfolioCompany(portfolioCompanyModel: any) {
    return portfolioCompanyModel?.companyList?.length > 0 &&
           portfolioCompanyModel?.moduleList?.length > 0 &&
           portfolioCompanyModel?.period?.length > 0 &&
           portfolioCompanyModel?.kpiItems?.length > 0;
  }
  validMandatoryESG(ESGModel: any) {
    return ESGModel?.companyList?.length > 0 &&
          ESGModel?.moduleList?.length > 0 &&
          ESGModel?.period?.length > 0 &&
          ESGModel?.kpiItems?.length > 0;
  }
  validMandatoryDeal(dealModel: any) {
    return dealModel?.companyList?.length > 0 &&
           dealModel?.fundList?.length > 0 &&
           dealModel?.period?.length > 0 &&
           dealModel?.moduleList?.length > 0 &&
           dealModel?.kpiItems?.length > 0;
  }
  validMandatoryFund(fundModel: any) {
    return fundModel?.fundList?.length > 0 &&
           fundModel?.period?.length > 0 &&
           fundModel?.moduleList?.length > 0 &&
           fundModel?.kpiItems?.length > 0;
  }
  tabDelete(tab: any) {
    if(this.defaultSelectedTab?.id >0){
      let dashboard = {
        File: "",
        DashboardId: this.defaultSelectedTab?.id,
        DatasetName: this.defaultSelectedTab?.dataSetName,
        UserId: this.userPermissions[0].userID,
        Description: this.description,
        isPersonal: this.isPersonal,
        isDeleted: true,
      };
      this.isLoader = true;
      this.dataAnalyticService.deleteDashboard(dashboard).subscribe({
        next: () => {
          this.reset();
          this.alertService.success("Dashboard deleted successfully", "", {
            positionClass: "toast-center-center",
          });
          this.currentTabId = 0;
          this.getDataAnalytics();
          this.isLoader = false;
          this.deletePopub = false;
        },
        error: (error) => {
          this.isLoader = false;
        },
      });
    }
  }
  tabCancel(tab: any) {
    this.deletePopub = false;
  }
  deletePopupFunction() {
    this.deletePopub = true;
  }
  hideModal() {
    this.displayModal = false;
  }
}
