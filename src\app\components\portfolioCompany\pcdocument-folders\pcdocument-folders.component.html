<div class="folder-list-container">
    <div class="header-container">
        <div>{{allFoldersAlias}}</div>
        <div>
            <img src="assets/dist/images/sort-folder-icon.svg" alt="Sort" />
        </div>
    </div>

    <div class="search-container">
        <div class="pl-3">
            <img src="assets/dist/images/search-folder-icon.svg" class="search-icon" alt="Search" />
        </div>
        <div>
            <input type="text" placeholder="Search" [(ngModel)]="searchTerm" (input)="filterFolders()">
        </div>
    </div>

    <div class="tree-container" *ngIf="filteredTreeData.length > 0">
        <div class="tree">
            <ng-container *ngTemplateOutlet="treeTemplate; context: { $implicit: filteredTreeData, level: 0 }"></ng-container>
        </div>
    </div>

    <div class="nodata-container" *ngIf="filteredTreeData.length === 0">
        <img src="assets/dist/images/no-folder-found.svg" alt="No Data" />
    </div>
</div>

<ng-template #treeTemplate let-nodes let-level="level">
    <ul class="tree-list">
        <li *ngFor="let node of nodes" class="tree-item">
            <div class="tree-item-content" [ngClass]="{'selected': node === selectedNode}" (click)="toggleFolder(node)">
                <div class="d-flex align-items-center flex-grow-1 node-data" [attr.data-level]="level"
                    [style.--level]="level"
                    [ngClass]="node.children === null ? (level === 0 ? 'pl-4' : level === 1 ? 'pl-44' : '') : ''">
                    <div class="arrow-icon" *ngIf="node.children !== null">
                        <img src="assets/dist/images/{{node.isExpanded ? 'right-arrow-icon.svg' :'up-arrow-icon.svg'}}"
                            alt="Arrow Icon" />
                    </div>
                    <div class="icon">
                        <img src="assets/dist/images/folder-icon.svg" class="folder-icon" alt="Folders" />
                    </div>
                    <div class="item-name">{{node.name}}</div>
                </div>
            </div>
            <div *ngIf="node.children && node.isExpanded" class="tree-children">
                <ng-container
                    *ngTemplateOutlet="treeTemplate; context: { $implicit: node.children, level: level + 1 }"></ng-container>
            </div>
        </li>
    </ul>
</ng-template>
<app-loader-component *ngIf="isLoading"></app-loader-component>