export interface kpiValueModel {
    oldValue: string;
    newValue: string;
    oldText: string;
    newText: string;
    comments: string;
}
export interface ModuleCompanyModel {
    moduleId: number;
    companyId: number;
    subPageId: number;
    valueType:string;
    capTableColPeriod?: string;
    capTablePeriodId?: number;
}
export interface TableHeader {
    header: string
    field: string
    isBold: boolean
    year: number
    quarter: any
    cell: any
}
export interface Audit {
    quarter?: string;
    year: number;
    month?: number;
    kpiValueId: number;
    mappingId:number;
    valueType:string;
    kpiId: number;
    moduleId: number;
    companyId: number;
    FieldName: string;
    valueTypeId?: number;
    periodId?: number;
    columnKpiId?: number;
}
export interface DataAuditLogValueModel {
    moduleId: number;
    attributeID: number;
    mappingId: number;
    portfolioCompanyId: number;
    oldValue: string;
    newValue: string;
    kpiId: number;
    valueType: string;
    description: string;
    quarter?: string;
    year: number;
    month?: number;
    commentId?: number;
    comments?: string;
    oldCurrencyType?: string;
    documentId?: number;
    supportingDocumentsId?: string;
    subPageId?: number;
    fieldName?: string;
    attributeName?: string;
    monthyear?: string;
    valueTypeId?: number;
    periodId?: number;
    columnKpiId?: number;
    columnNumber?: number;
}
export interface MappedDocuments {
    documentId: number;
    commentId: number;
    valueId: number;
    comments: string;
    supportingDocumentsId: string;
    mappingId: number;
    documentModels: DocumentModel[];
    auditLogId: number;
    auditLogCount: number;
}
export interface DocumentModel {
    id?: number;
    documentId: string;
    documentName: string;
    extension: string;
    isExisting: boolean;
}
export interface UpdatedFiles {
    documentId: string;
    file: any;
}