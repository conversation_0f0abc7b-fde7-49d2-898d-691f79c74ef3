import { TestBed } from '@angular/core/testing';

import { PanelbarItemService } from './panelbar-item.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { FeatureTableMapping } from 'src/app/common/constants';

describe('PanelbarItemService', () => {
  let service: PanelbarItemService; 
  let httpMock: HttpTestingController;
  const mockBaseUrl = 'http://localhost:4200/';
  const mockPanelbarItems = [{ id: 1, subPageDetailList: [] }];
  const mockCompany = { id: 1, name: 'Company 1' };
  const mockItems = [{ id: 2, parentId: 1 }];
  let permissionsMock: any;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PanelbarItemService,
        { provide: 'BASE_URL', useValue: mockBaseUrl }
      ]
    });

    service = TestBed.inject(PanelbarItemService);
    httpMock = TestBed.inject(HttpTestingController);
    permissionsMock = [
      { subFeatureId: 1, CAN_VIEW: true },
      { subFeatureId: 2, CAN_VIEW: false }
    ];
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should set company', () => {
    spyOn(service.selectedCompany, 'next');
    service.setCompany(mockCompany);
    expect(service.selectedCompany.next).toHaveBeenCalledWith(mockCompany);
  });

  it('should get company as observable', () => {
    service.selectedCompany.next(mockCompany);
    service.getCompany().subscribe(company => {
      expect(company).toEqual(mockCompany);
    });
  });

  it('should get investment company list', () => {
    const mockResponse = [{ id: 1, name: 'Investment Company 1' }];

    service.getInvestCompanyList().subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(mockBaseUrl + 'api/v1/InvestmentCompany/investment-company/get');
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should handle error', () => {
    const mockError = { status: 500, statusText: 'Server Error' };

    service.getInvestCompanyList().subscribe(
      () => fail('should have failed with the 500 error'),
      (error) => {
        expect(error.status).toEqual(500);
        expect(error.statusText).toEqual('Server Error');
      }
    );

    const req = httpMock.expectOne(mockBaseUrl + 'api/v1/InvestmentCompany/investment-company/get');
    req.flush(null, mockError);
  });

  it('should update sequenceNo of Investment_Summary based on Company_Facts', () => {
    const tableList = [
      { tableId: 1, sequenceNo: 1, isShow: true },
      { tableId: 2, sequenceNo: 2, isShow: true }
    ];

    service.updateTableVisibility(tableList, permissionsMock);

    expect(tableList[0].sequenceNo).toBe(1);
  });

  it('should set isShow based on permissions', () => {
    const tableList = [
      { tableId: 1, sequenceNo: 1, isShow: true },
      { tableId: 2, sequenceNo: 2, isShow: true }
    ];
    spyOn(service, 'checkTablePermissions').and.callFake((tableId) => {
      return tableId === 1;
    });

    service.updateTableVisibility(tableList, permissionsMock);

    expect(tableList[0].isShow).toBe(true);
    expect(tableList[1].isShow).toBe(false);
  });

  it('should set isShow to false for ToBeChangeTableIds', () => {
    const tableList = [
      { tableId: 1, sequenceNo: 1, isShow: true },
      { tableId: 2, sequenceNo: 2, isShow: true }
    ];

    service.updateTableVisibility(tableList, permissionsMock);

    expect(tableList[0].isShow).toBe(false);
    expect(tableList[1].isShow).toBe(false);
  });
  it('should update table visibility correctly', () => {
    const tableList = [
      { tableId: FeatureTableMapping.TABLES_NAME.Investment_Summary, sequenceNo: 1, isShow: true },
      { tableId: FeatureTableMapping.TABLES_NAME.Company_Facts, sequenceNo: 2, isShow: true },
      // Add more table items as needed
    ];
    const permissions = [
      { subFeatureId: 1, CAN_VIEW: true },
      // Add more permissions as needed
    ];

    service.updateTableVisibility(tableList, permissions);

    // Expectations
    expect(tableList[0].sequenceNo).toBe(3); // Assuming Company_Facts sequenceNo is 2
    expect(tableList[0].isShow).toBe(false); // Assuming permissions allow viewing
    expect(tableList[1].isShow).toBe(false); // Assuming permissions allow viewing

    // Add more expectations based on your logic
  });
});
