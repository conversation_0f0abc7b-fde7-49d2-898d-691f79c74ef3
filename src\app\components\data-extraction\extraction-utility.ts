import { FormArray } from "@angular/forms";
import { FileDetails, IngestionFormData, SelectedFile } from "./data-ingestion/data-ingestion.model";
import { CommonFileModel, DocumentSummaryDto, IngestConfigurationModel, ProcessDetailsDto, S3UrlExtractedModel, StateAndStatus, UploadResponse } from "./extraction.model";
import { CommonConstants } from "src/app/common/constants";
export class ExtractionUtility {
  static extractS3Urls(
    uploadResponse: UploadResponse[]
  ): S3UrlExtractedModel[] {
    const processedUrls: S3UrlExtractedModel[] = [];
    uploadResponse.forEach((response) => {
      if (response.url) {
        const documentsIndex = response.url.indexOf("Documents/");
        if (documentsIndex !== -1) {
          const url = response.url.substring(
            documentsIndex + "Documents/".length
          );
          const segments = url.split("/");
          if (segments.length >= 3) {
            const extractedModel: S3UrlExtractedModel = {
              jobId: "00000000-0000-0000-0000-000000000000",
              processId: segments[1] || null,
              tenantId: segments[0] || null,
              filename: segments[2] || null,
              statusId: null,
              s3Url: response.url,
              parentJobId: null,
            };
            processedUrls.push(extractedModel);
          }
        }
      }
    });
    return processedUrls;
  }

  /**
   * Transforms S3UrlExtractedModel data into file format
   * @param s3UrlModels Array of S3UrlExtractedModel objects
   * @returns Object with files array containing file_name, s3_path, source, and file_id
   */
  public static transformToFileFormat(s3UrlModels: S3UrlExtractedModel[]): CommonFileModel[] {
    const files: CommonFileModel[] = s3UrlModels.map(model => {
      return {
        file_name: model.filename,
        s3_path: model.s3Url,
        source: "upload"
      } as CommonFileModel;
    });
    return files;
  }

  /**
   * Creates form data for file uploads
   * @param ingestionFormData The ingestion form data
   * @param selectedFilesList List of selected files
   * @param formArray Form array containing file metadata
   * @param featureId Optional feature ID (defaults to '14')
   * @returns FormData object populated with all necessary fields
   */
  public static createUploadFormData(
    ingestionFormData: IngestionFormData,
    selectedFilesList: SelectedFile[],
    formArray: FormArray,
    featureId: string = '14'
  ): FormData {
    const formData = new FormData();
    // Add ingestion form data
    featureId=ingestionFormData.module.id.toString();
    formData.append('SourceId', ingestionFormData.sourceType.id.toString());
    formData.append('CompanyId', ingestionFormData.company==null?'': ingestionFormData.company.companyId.toString());
    formData.append('FeatureId', featureId);
    formData.append('ExtractionType', ingestionFormData.extractionType);
    formData.append('FundId', (ingestionFormData.funds !=null ||ingestionFormData.funds !=undefined)? ingestionFormData.funds.fundId.toString() : '0');
    formData.append('CompanyIssuers', ingestionFormData.companyIssuers.length > 0 ? JSON.stringify(ingestionFormData.companyIssuers) : ''); 
    // Add file data
    selectedFilesList.forEach((selectedFile, index) => {
      const fileGroup = formArray.at(index);
      if (selectedFile.errors) {
        const errorsValue = Array.isArray(selectedFile.errors) 
          ? JSON.stringify(selectedFile.errors) 
          : selectedFile.errors;
        formData.append(`Files[${index}].Errors`, errorsValue);
      }
      formData.append(`Files[${index}].File`, selectedFile.file);    
    });

    return formData;
  }
  public static monthNumber(text: string): string {
    const month = CommonConstants.monthOptions.find((month: { text: string; number: number }) => month.text === text);
    return month ? month.number.toString() : '0';  
  }
 public static processS3Response(responses: UploadResponse[]): { 
    extractedModel: S3UrlExtractedModel[], 
    fileFormat: CommonFileModel[]
  } {
    const extractedModel = ExtractionUtility.extractS3Urls(responses);
    let fileFormat = null;
    
    if (extractedModel.length > 0) {
      fileFormat = ExtractionUtility.transformToFileFormat(extractedModel);
    }    
    return { extractedModel, fileFormat };
  }
  public static mapModelProperties<T>(source: any, properties: string[]): T {
    const target = {} as T;
    
    for (const prop of properties) {
      target[prop] = source?.[prop] || null;
    }
    
    return target;
  }
  public static getIdByStatusName(name: string, stateAndStatus: StateAndStatus[]): string {
    const state = stateAndStatus.find(state => state.state === name);
    return state ? state.id : null;
  }
  public static updateSelectedFilesFromDocuments(documents:DocumentSummaryDto[]): FileDetails[] {
    return documents.map(document => {
      return { id: document.id, name: document.name };
    });
  }
  public static uploadNewFilesFormData(
    selectedFilesList: SelectedFile[],
    formArray: FormArray,
    UpdateProcessId: string,
    extractionType: string
  ): FormData {
    const formData = new FormData();
    formData.append('UpdateProcessId', UpdateProcessId);
    formData.append('ExtractionType', extractionType);
    selectedFilesList.forEach((selectedFile, index) => {
      const fileGroup = formArray.at(index);
      if (fileGroup != undefined) {
        const pages = fileGroup.get("pages").value;
        formData.append(`Files[${index}].Pages`, JSON.stringify(pages));
      }
      formData.append('File', selectedFile.file);
      formData.append(`Files[${index}].File`, selectedFile.file);
      formData.append(`Files[${index}].Errors`, "");    
    });
    return formData;
  }
  public static createIngestConfigurationModel(fileFormatModel: CommonFileModel[],client_id:string="foliosure",processDetails: ProcessDetailsDto): IngestConfigurationModel {
      return ({
        app_name: "foliosure",
        output_version: "24-03-2025",
        client_id: client_id,
        session_id: "",
        job_type: "spread",
        notes_extraction: false,
        is_llm_agent: false,
        job_engine: "acuity",
        company_id: processDetails.extractionType=="Specific KPI"? processDetails.fundId.toString(): processDetails.encryptedPortfolioCompanyId,
        company_name:processDetails.extractionType=="Specific KPI"? processDetails.fundName:  processDetails.companyName,
        country: "India",
        sector: "Energy",
        ticker: "Ticker",
        exchange: "",
        template: false,
        template_id: "",
        as_reported: true,
        kpi_json: false,
        industry: "PE",
        files: fileFormatModel,
        output_mode: "s3",
      } as IngestConfigurationModel);
    }
    public static isValidGuid(guid: string): boolean {
      return guid && guid !== "00000000-0000-0000-0000-000000000000";
    }
    /**
   * Determines available period types based on the structure of repository data
   * @param structure Repository structure containing year nodes with children
   * @returns Array of period type objects with id and name
   */
  public static determineAvailablePeriodTypes(structure: any): { id: string; name: string }[] {
    if (!structure?.children?.length) {
      return [];
    }

    const periodTypes = [{
      id: 'year',
      name: 'Year'
    }];

    const hasQuarters = structure.children.some(yearNode =>
      yearNode.children?.some(child =>
        child.name.toLowerCase().includes('quarter')
      )
    );

    const hasMonths = structure.children.some(yearNode =>
      yearNode.children?.some(child =>
        /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i.test(child.name)
      )
    );

    if (hasQuarters) {
      periodTypes.push({ id: 'quarter', name: 'Quarter' });
    }

    if (hasMonths) {
      periodTypes.push({ id: 'month', name: 'Month' });
    }

    return periodTypes.sort((a, b) => a.name.localeCompare(b.name));
  }
}