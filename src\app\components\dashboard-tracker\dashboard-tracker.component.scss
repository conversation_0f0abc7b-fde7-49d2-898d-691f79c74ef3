@import '../../../variables';

$space-24: 24px;

.dashboard-tracker-table{
  height: 75vh;
  border-radius: $Radius-4;
  overflow: auto;
}

.configuration-dashboard {
  border-radius: 0 !important;
  border-left: 0 !important;
  border-right: 0 !important;
}

.text-logo{
  border-radius: $Radius-4;
  height: $space-24;
  width: $space-24;
  display: flex;
  justify-content: center;
  align-items: center;
  color: $Neutral-Gray-00;
  background: linear-gradient(180deg, #021155 0%, #9C27B0 100%);
}

.company-logo {
  vertical-align: middle;
  border-radius: $Radius-4;
  height: $space-24;
  width: $space-24;  
  background: $Neutral-Gray-00 !important;
  border: 1px solid $Neutral-Gray-10;
  object-fit: contain;
}

.header-title{
  color: $Neutral-Gray-90;
}

.dashboard-tracker-table input[type="checkbox"] {
  border: 1px solid $Neutral-Gray-60 !important;
}

.dropdown-input{
  overflow: visible;
  text-overflow: unset;
  white-space: normal;
  padding-right: 0;
}

.kendo-container{
  overflow-x: auto;
  width: 100%;
}