import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { AddEmailGroupComponent } from './add-email-group.component';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { Router, ActivatedRoute } from '@angular/router';
import { of, Subject, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { GridModule } from '@progress/kendo-angular-grid';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { RepositorySharedModule } from '../../shared/repository-shared.module';

describe('AddEmailGroupComponent', () => {
  let component: AddEmailGroupComponent;
  let fixture: ComponentFixture<AddEmailGroupComponent>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let emailGroupServiceSpy: jasmine.SpyObj<RepositoryConfigService>;
  let routerSpy: jasmine.SpyObj<Router>;
  let routeParams$: Subject<any>;

  beforeEach(async () => {
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);
    emailGroupServiceSpy = jasmine.createSpyObj('RepositoryConfigService', [
      'getEmailGroupDetails', 'createEmailGroup', 'updateEmailGroup'
    ]);
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    routeParams$ = new Subject();

    await TestBed.configureTestingModule({
      declarations: [AddEmailGroupComponent],
      imports: [
        FormsModule,
        DialogModule,
        GridModule,
        ButtonsModule,
        SharedComponentModule,
        RepositorySharedModule
      ],
      providers: [
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: RepositoryConfigService, useValue: emailGroupServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: { params: routeParams$.asObservable() } }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(AddEmailGroupComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should add a new member row', () => {
    component.newMembers = [{ name: 'A', email: '<EMAIL>' }];
    component.addNewMember();
    expect(component.newMembers.length).toBe(2);
  });

  it('should remove a member row if more than one exists', () => {
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.removeNewMember(0);
    expect(component.newMembers.length).toBe(1);
  });

  it('should warn if trying to remove the last member', () => {
    component.newMembers = [{ name: 'A', email: '<EMAIL>' }];
    component.removeNewMember(0);
    expect(toastrServiceSpy.warning).toHaveBeenCalled();
    expect(component.newMembers.length).toBe(1);
    expect(component.newMembers[0]).toEqual({ name: '', email: '' });
  });

  it('should reset the form', () => {
    component.newGroupName = 'Test';
    component.newMembers = [{ name: 'A', email: '<EMAIL>' }];
    component.selectedCompanies = [{ id: 1 }];
    component.resetForm();
    expect(component.newGroupName).toBe('');
    expect(component.newMembers.length).toBe(3);
    expect(component.selectedCompanies.length).toBe(0);
  });

  it('should set selected companies', () => {
    const companies = [{ id: 1 }, { id: 2 }];
    component.onCompanySelected(companies);
    expect(component.selectedCompanies).toBe(companies);
  });

  it('should not enable create if group name is empty', () => {
    component.newGroupName = ' ';
    expect(component.isCreateEnabled()).toBeFalse();
    component.newGroupName = 'Test';
    expect(component.isCreateEnabled()).toBeTrue();
  });

  it('should reset and navigate on cancel', () => {
    spyOn(component, 'resetForm');
    component.cancel();
    expect(component.resetForm).toHaveBeenCalled();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/email-configuration'], { queryParams: { activeTab: 'Email Groups' } });
  });

  it('should call createEmailGroup and create a new group successfully', fakeAsync(() => {
    component.isEditMode = false;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }, { id: 2 }];

    // Mock duplicate name check (not duplicate)
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: false }));
    // Mock createEmailGroup
    emailGroupServiceSpy.createEmailGroup.and.returnValue(of({ groupId: 123 }));
    spyOn(component, 'resetForm');

    component.createNewEmailGroup();
    tick();

    expect(emailGroupServiceSpy.checkDuplicateName).toHaveBeenCalled();
    expect(emailGroupServiceSpy.createEmailGroup).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalledWith(
      'You have successfully created email group', '', { positionClass: 'toast-center-center' }
    );
    expect(component.resetForm).toHaveBeenCalled();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/email-configuration'], { queryParams: { activeTab: 'Email Groups' } });
  }));

  it('should show error if duplicate group name on createNewEmailGroup', fakeAsync(() => {
    component.isEditMode = false;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];

    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: true }));
    component.createNewEmailGroup();
    tick();
    expect(component.hasError).toBeTrue();
    expect(emailGroupServiceSpy.createEmailGroup).not.toHaveBeenCalled();
  }));

  it('should call updateEmailGroup and update group successfully', fakeAsync(() => {
    component.isEditMode = true;
    component.id = 5;
    component.newGroupName = 'Updated Group';
    component.newMembers = [
      { id: 1, name: 'A', email: '<EMAIL>' },
      { id: 2, name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];

    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: false }));
    emailGroupServiceSpy.updateEmailGroup.and.returnValue(of({}));

    component.updateEmailGroup();
    tick();

    expect(emailGroupServiceSpy.checkDuplicateName).toHaveBeenCalled();
    expect(emailGroupServiceSpy.updateEmailGroup).toHaveBeenCalledWith(5, jasmine.any(Object));
    expect(toastrServiceSpy.success).toHaveBeenCalledWith(
      'You have successfully updated email group', '', { positionClass: 'toast-center-center' }
    );
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/email-configuration'], { queryParams: { activeTab: 'Email Groups' } });
  }));

  it('should show error if duplicate group name on updateEmailGroup', fakeAsync(() => {
    component.isEditMode = true;
    component.id = 5;
    component.newGroupName = 'Updated Group';
    component.newMembers = [
      { id: 1, name: 'A', email: '<EMAIL>' },
      { id: 2, name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];

    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: true }));
    component.updateEmailGroup();
    tick();
    expect(component.hasError).toBeTrue();
    expect(emailGroupServiceSpy.updateEmailGroup).not.toHaveBeenCalled();
  }));

  it('should handle error in createEmailGroup API', fakeAsync(() => {
    component.isEditMode = false;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: false }));
    emailGroupServiceSpy.createEmailGroup.and.returnValue(throwError({ message: 'create error' }));
    component.createNewEmailGroup();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('create error');
  }));

  it('should handle error in updateEmailGroup API', fakeAsync(() => {
    component.isEditMode = true;
    component.id = 1;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { id: 1, name: 'A', email: '<EMAIL>' },
      { id: 2, name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: false }));
    emailGroupServiceSpy.updateEmailGroup.and.returnValue(throwError({ message: 'update error' }));
    component.updateEmailGroup();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('update error');
  }));

  it('should handle error in checkDuplicateName for create', fakeAsync(() => {
    component.isEditMode = false;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(throwError({}));
    component.createNewEmailGroup();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Failed to check for duplicate group name');
  }));

  it('should handle error in checkDuplicateName for update', fakeAsync(() => {
    component.isEditMode = true;
    component.id = 1;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { id: 1, name: 'A', email: '<EMAIL>' },
      { id: 2, name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(throwError({}));
    component.updateEmailGroup();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Failed to check for duplicate group name');
  }));

  it('should set hasError to true on duplicate name and reset on success', fakeAsync(() => {
    component.isEditMode = false;
    component.newGroupName = 'Test Group';
    component.newMembers = [
      { name: 'A', email: '<EMAIL>' },
      { name: 'B', email: '<EMAIL>' }
    ];
    component.selectedCompanies = [{ id: 1 }];
    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: true }));
    component.createNewEmailGroup();
    tick();
    expect(component.hasError).toBeTrue();

    emailGroupServiceSpy.checkDuplicateName = jasmine.createSpy().and.returnValue(of({ isDuplicateName: false }));
    emailGroupServiceSpy.createEmailGroup.and.returnValue(of({ groupId: 123 }));
    component.createNewEmailGroup();
    tick();
    expect(component.hasError).toBeFalse();
  }));

  it('should delegate to updateEmailGroup or createNewEmailGroup based on isEditMode', () => {
    spyOn(component, 'updateEmailGroup');
    spyOn(component, 'createNewEmailGroup');
    component.isEditMode = true;
    component.createEmailGroup();
    expect(component.updateEmailGroup).toHaveBeenCalled();
    component.isEditMode = false;
    component.createEmailGroup();
    expect(component.createNewEmailGroup).toHaveBeenCalled();
  });

  it('should handle onCompanySelected with empty/undefined', () => {
    component.onCompanySelected(undefined as any);
    expect(component.selectedCompanies).toBeUndefined();
    component.onCompanySelected([]);
    expect(component.selectedCompanies).toEqual([]);
  });
});

