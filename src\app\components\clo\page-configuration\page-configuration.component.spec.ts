import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PageConfigurationComponent } from './page-configuration.component';
import { AccountService } from 'src/app/services/account.service';
import { InvestCompanyService } from 'src/app/components/clo/investmentcompany/investmentcompany.service';
import { of, throwError } from 'rxjs';
import { MatMenuModule } from '@angular/material/menu';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { PanelbarItemComponent1 } from '../shared/panelbar-item/panelbar-item.component';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { ToastrService } from 'ngx-toastr';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { LoaderComponent } from 'projects/ng-neptune/src/lib/loader-component/loader-component.component';
import { DragDropModule } from '@angular/cdk/drag-drop';

describe('PageConfigurationComponent', () => {
  let component: PageConfigurationComponent;
  let fixture: ComponentFixture<PageConfigurationComponent>;
  let accountServiceMock: any;
  let investCompanyServiceMock: any;
  let panelbarItemService: jasmine.SpyObj<PanelbarItemService>;
  const panelbarItemServiceSpy = jasmine.createSpyObj('PanelbarItemService', ['getInvestCompanyList', 'getCompany', 'getCLOPageDetails','getTabList', 'setCompany','saveTabDetails','updateTableVisibility']);
  let toastrService: jasmine.SpyObj<ToastrService>;

  const mockCompanies = [{
    id: 1,
    companyName: "Company A",
    domicile: "bmb",
    incorporationDate: "20 February 2025",
    firstClose: "20 February 2025",
    finalClose: "20 February 2025",
    investmentPeriodEndDate: "20 February 2025",
    maturityDate: "20 February 2025",
    commitments: "bm",
    baseCurrency: "bnmb",
    custodian: "bb",
    administrator: "bnb",
    listingAgent: "nb",
    legalCounsel: "bnmb",
    portfolioAdvisor: "mbm",
    investmentSummary: "kkjhkjln ",
    createdOn: "2025-02-20T17:13:57.3",
    createdBy: 0,
    modifiedOn: null,
    modifiedBy: null
}];
const mockPages = [
{
    pageID: 1,
    name: "Page A",
    aliasName: "Page A",
    isActive: true,
    isDeleted: false,
    createdOn: "0001-01-01T00:00:00",
    createdBy: 0,
    modifiedOn: null,
    modifiedBy: null 
  }, 
    {
         pageID: 2,
          name: "Page B",
          aliasName: "Page B",
          isActive: true,
          isDeleted: false,
          createdOn: "0001-01-01T00:00:00",
          createdBy: 0,
          modifiedOn: null,
          modifiedBy: null 
}];
const mockCompany = {
id: 1,
companyName: "Company A",
domicile: "bmb",
incorporationDate: "20 February 2025",
firstClose: "20 February 2025",
finalClose: "20 February 2025",
investmentPeriodEndDate: "20 February 2025",
maturityDate: "20 February 2025",
commitments: "bm",
baseCurrency: "bnmb",
custodian: "bb",
administrator: "bnb",
listingAgent: "nb",
legalCounsel: "bnmb",
portfolioAdvisor: "mbm",
investmentSummary: "kkjhkjln ",
createdOn: "2025-02-20T17:13:57.3",
createdBy: 0,
modifiedOn: null,
modifiedBy: null
};
const mockPage = { 
  pageID: 1,
  name: "Page A",
  aliasName: "Page A",
  isActive: true,
  isDeleted: false,
  createdOn: "0001-01-01T00:00:00",
  createdBy: 0,
  modifiedOn: null,
  modifiedBy: null 
};
const mockTabList = [
  {
    id: 9,
    tabId: 2,
    pageId: 1,
    companyId: 3013,
    name: "Page A",
    aliasName: "Page A",
    isDeleted: false,
    sequenceNo: 1,
    isActive: true,
    parentId: 0,
    subTabList: [
        {
            id: 10,
            tabId: 8,
            pageId: 1,
            companyId: 3013,
            name: "Sub Page A",
            aliasName: "Sub Page A1111",
            isDeleted: false,
            sequenceNo: 1,
            isActive: true,
            parentId: 2,
            subTabList: null,
            createdOn: "0001-01-01T00:00:00",
            createdBy: 0,
            modifiedOn: null,
            modifiedBy: null
        }
    ],
    createdOn: "0001-01-01T00:00:00",
    createdBy: 0,
    modifiedOn: null,
    modifiedBy: null
}
];

  beforeEach(async () => {
    toastrService = jasmine.createSpyObj("ToastrService", [
      "success",
      "error",
      "info",
      "warning",
    ]);
    await  
    TestBed.configureTestingModule({
      declarations: [PageConfigurationComponent,PanelbarItemComponent1,LoaderComponent],
      imports: [MatMenuModule, HttpClientTestingModule,DropDownsModule,FormsModule,BrowserAnimationsModule,DragDropModule ],
      providers: [
        { provide: AccountService, useValue: accountServiceMock },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
        { provide: InvestCompanyService, useValue: investCompanyServiceMock },
         { provide: ToastrService, useValue: toastrService},
         { provide: PanelbarItemService, useValue: panelbarItemServiceSpy }
      ]
    }).compileComponents();

    
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PageConfigurationComponent);
    component = fixture.componentInstance;
    panelbarItemService = TestBed.inject(PanelbarItemService) as jasmine.SpyObj<PanelbarItemService>;
    
    panelbarItemService.getInvestCompanyList.and.returnValue(of([]));
    panelbarItemService.getCompany.and.returnValue(of([]));
    panelbarItemService.getCLOPageDetails.and.returnValue(of([mockPages]));
    panelbarItemService.getTabList.and.returnValue(of([mockTabList]));
    
    accountServiceMock = {
      getUserPermissionByEmail: jasmine.createSpy('getUserPermissionByEmail').and.returnValue(of({ features: [{ aliasName: 'Page1' }, { aliasName: 'Page2' }] }))
    };
  fixture.detectChanges();
  });
  it('should initialize correctly in ngOnInit', () => {
   

    panelbarItemService.getInvestCompanyList.and.returnValue(of(mockCompanies));
    panelbarItemService.getCompany.and.returnValue(of(mockCompany));
    panelbarItemService.getCLOPageDetails.and.returnValue(of(mockPages));

    component.ngOnInit();

    expect(panelbarItemService.getInvestCompanyList).toHaveBeenCalled();
    expect(panelbarItemService.getCompany).toHaveBeenCalled();
    expect(panelbarItemService.getCLOPageDetails).toHaveBeenCalled();

    expect(component.compnayList).toEqual(mockCompanies);
    expect(component.selectedCompanyItem).toEqual(mockCompany);
    expect(component.pageDropdownOptions).toEqual(mockPages);
    expect(component.selectedPageItem).toEqual(mockPages[0]);
    expect(component.selectedPageTypeName).toEqual(mockPages[0].name);
  });

  it('should handle empty company list', () => {
    panelbarItemService.getInvestCompanyList.and.returnValue(of([]));
    panelbarItemService.getCompany.and.returnValue(of(null));
    panelbarItemService.getCLOPageDetails.and.returnValue(of([]));

    component.ngOnInit();

    expect(panelbarItemService.getInvestCompanyList).toHaveBeenCalled();
    expect(panelbarItemService.getCompany).toHaveBeenCalled();
    expect(panelbarItemService.getCLOPageDetails).toHaveBeenCalled();

    expect(component.compnayList).toBeUndefined();
    expect(component.selectedCompanyItem).toEqual(null);
    expect(component.pageDropdownOptions).toEqual([]);
    expect(component.selectedPageItem).toBeUndefined();
    expect(component.selectedPageTypeName).toBeUndefined();
  });
  it('should set selectedCompanyItem and selectedPageItem on init', () => {
    fixture.detectChanges(); // triggers ngOnInit

  component.selectedCompanyItem = mockCompany;
  component.selectedPageItem=mockPage;
    expect(component.selectedCompanyItem).toEqual(mockCompany);
    expect(component.selectedPageItem).toEqual(mockPage);
  });
  
  it('should call getConfiguration if lastSelectedPageItem is not empty', () => {
    component.lastSelectedPageItem = mockPage;
  spyOn(component, 'getConfiguration');
  panelbarItemService.getCLOPageDetails.and.returnValue(of(mockPages));

  fixture.detectChanges(); // triggers ngOnInit
  component.ngOnInit(); // explicitly call ngOnInit if needed
  component.selectedPageItem = mockPage;

  expect(component.selectedPageItem).toEqual(mockPage);
  expect(component.getConfiguration).toHaveBeenCalled();
});
  it('should subscribe to getCompany and set selectedCompanyItem', () => {
    fixture.detectChanges(); // triggers ngOnInit
  component.selectedCompanyItem=mockCompany;
    expect(component.selectedCompanyItem).toEqual(mockCompany);
  });
  
  it('should subscribe to getCLOPageDetails and set pageDropdownOptions', () => {
    fixture.detectChanges(); // triggers ngOnInit
    component.pageDropdownOptions = mockPages;
    component.selectedPageItem = mockPages[0];
    component.selectedPageTypeName = mockPages[0].name;
  
    expect(component.pageDropdownOptions).toEqual(mockPages);
    expect(component.selectedPageItem).toEqual(mockPages[0]);
    expect(component.selectedPageTypeName).toEqual(mockPages[0].name);
  });

  it('should find the parent element by selector', () => {
    const parent = document.createElement('div');
    parent.classList.add('parent');
    const child = document.createElement('div');
    parent.appendChild(child);
    document.body.appendChild(parent);

    const result = component.findParentBySelector(child, '.parent');
    expect(result).toBe(parent);

    document.body.removeChild(parent);
  });
  
  it('should transform subPageDetailList correctly', () => {
    const subPageDetailList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Page A",
        aliasName: "Page A",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Page A",
                aliasName: "Sub Page A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null
    }
    ];

    const expectedTransformedList = [
      {
        id: 9,
        tabId: 2,
        pageId: 1,
        companyId: 3013,
        name: "Page A",
        aliasName: "Page A",
        isDeleted: false,
        sequenceNo: 1,
        isActive: true,
        parentId: 0,
        subTabList: [
            {
                id: 10,
                tabId: 8,
                pageId: 1,
                companyId: 3013,
                name: "Sub Page A",
                aliasName: "Sub Page A1111",
                isDeleted: false,
                sequenceNo: 1,
                isActive: true,
                parentId: 2,
                subTabList: null,
                createdOn: "0001-01-01T00:00:00",
                createdBy: 0,
                modifiedOn: null,
                modifiedBy: null,
                isTabExpanded: false,
          isCustomFieldSupported: false
            }
        ],
        createdOn: "0001-01-01T00:00:00",
        createdBy: 0,
        modifiedOn: null,
        modifiedBy: null,
        isTabExpanded: false,
        isCustomFieldSupported: false
    }
    ]

    const result = component.transformSubPageDetailList(subPageDetailList);
    expect(result).toEqual(expectedTransformedList);
  }); 
  it('should update isDisabledBtn and subPageList based on the event', () => {
    // Arrange
    const mockEvent = {
      isDisabledBtn: true,
      subPageList: [{ id: 1, name: 'Sub Page 1' }]
    };
  
    // Act
    component.checkAnyDataChange(mockEvent);
  
    // Assert
    expect(component.isDisabledBtn).toBe(true);
    expect(component.subPageList).toEqual([{ id: 1, name: 'Sub Page 1' }]);
  });

  it('should update aliasName and sequenceNo based on editChange and toBeChange mappings', () => {
    const tableList = [
      { tableId: 1, aliasName: 'Old Alias 1', sequenceNo: 1 },
      { tableId: 2, aliasName: 'Old Alias 2', sequenceNo: 2 }
    ];

    // spyOn(FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME, 'key').and.returnValue([1]);
    // spyOn(FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME, 'key').and.returnValue([2]);

    component.updateIds(tableList);

    expect(tableList[0].aliasName).toBe('Old Alias 1');
    expect(tableList[0].sequenceNo).toBe(1);
  });

  it('should not update if tableList is null or undefined', () => {
    const tableList = null;

    component.updateIds(tableList);

    expect(tableList).toBeNull();
  });

  it('should not update if editIndex or changeIndex is not found', () => {
    const tableList = [
      { tableId: 1, aliasName: 'Old Alias 1', sequenceNo: 1 },
      { tableId: 3, aliasName: 'Old Alias 3', sequenceNo: 3 }
    ];

    // spyOn(FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME, 'key').and.returnValue([1]);
    // spyOn(FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME, 'key').and.returnValue([2]);

    component.updateIds(tableList);

    expect(tableList[0].aliasName).toBe('Old Alias 1');
    expect(tableList[0].sequenceNo).toBe(1);
  });

  it('should update companyId and cloId, and call saveTabDetails', () => {
    component.selectedCompanyItem = { id: 1 };
    component.selectedCLOItem = { clO_ID: 2 };
    component.subPageList = [
      { companyId: null, cloId: null, subTabList: [{ cloId: null, tableList: [{ cloId: null }] }], tableList: [{ cloId: null }] }
    ];

    panelbarItemService.saveTabDetails.and.returnValue(of({ success: true, message: 'Success' }));
    spyOn(component, 'updateNestedItem');
    spyOn(component, 'getConfiguration');

    component.save();

    expect(component.isPopup).toBe(false);
    expect(component.subPageList[0].companyId).toBe(1);
    expect(component.subPageList[0].cloId).toBe(2);
    expect(component.subPageList[0].subTabList[0].cloId).toBe(2);
    expect(component.subPageList[0].subTabList[0].tableList[0].cloId).toBe(2);
    expect(component.subPageList[0].tableList[0].cloId).toBe(2);
    expect(component.updateNestedItem).toHaveBeenCalled();
    expect(panelbarItemService.saveTabDetails).toHaveBeenCalledWith(component.subPageList);
    expect(component.getConfiguration).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Success', "", { positionClass: "toast-center-center" });
  });

  it('should handle error in saveTabDetails', () => {
    component.selectedCompanyItem = { id: 1 };
    component.selectedCLOItem = { clO_ID: 2 };
    component.subPageList = [
      { companyId: null, cloId: null, subTabList: [{ cloId: null, tableList: [{ cloId: null }] }], tableList: [{ cloId: null }] }
    ];

    panelbarItemService.saveTabDetails.and.returnValue(throwError({ message: 'Error' }));

    component.save();

    expect(toastrService.error).toHaveBeenCalledWith('Error', "", { positionClass: "toast-center-center" });
  });
   
});
