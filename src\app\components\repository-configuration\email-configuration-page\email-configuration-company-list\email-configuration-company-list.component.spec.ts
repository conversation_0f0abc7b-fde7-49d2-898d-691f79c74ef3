import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailConfigurationCompanyListComponent } from './email-configuration-company-list.component';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { EventEmitter } from '@angular/core';

describe('EmailConfigurationCompanyListComponent', () => {
  let component: EmailConfigurationCompanyListComponent;
  let fixture: ComponentFixture<EmailConfigurationCompanyListComponent>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;

  // Mock data
  const mockCompanies = [
    { companyName: 'Company A', portfolioCompanyID: 1 },
    { companyName: 'Company B', portfolioCompanyID: 2 },
    { companyName: 'Company C', portfolioCompanyID: 3 }
  ];

  beforeEach(async () => {
    // Create spy for repository config service
    const repoConfigSpy = jasmine.createSpyObj('RepositoryConfigService', 
      ['getPortfolioCompanies'], 
      { resetInProgress$: of(false) }
    );
    
    // Configure spy to return mock data
    repoConfigSpy.getPortfolioCompanies.and.returnValue(of(mockCompanies));

    await TestBed.configureTestingModule({
      declarations: [EmailConfigurationCompanyListComponent],
      imports: [FormsModule, HttpClientTestingModule],
      providers: [
        { provide: RepositoryConfigService, useValue: repoConfigSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // For app-loader-component
    }).compileComponents();

    repositoryConfigServiceSpy = TestBed.inject(RepositoryConfigService) as jasmine.SpyObj<RepositoryConfigService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EmailConfigurationCompanyListComponent);
    component = fixture.componentInstance;
    
    // The key change: Replace the EventEmitter with our own that we can spy on
    component.selectedCompanies = new EventEmitter<any[]>();
    spyOn(component.selectedCompanies, 'emit').and.callThrough();
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load companies on init', () => {
    expect(repositoryConfigServiceSpy.getPortfolioCompanies).toHaveBeenCalled();
    expect(component.companies.length).toBe(3);
    expect(component.companies[0].name).toBe('Company A');
    expect(component.companies[0].companyId).toBe(1);
    expect(component.companies[0].selected).toBeFalse();
  });

  it('should filter companies based on search term', () => {
    // Set search term
    component.searchTerm = 'Company A';
    
    // Check filtered results
    expect(component.filteredCompanies.length).toBe(1);
    expect(component.filteredCompanies[0].name).toBe('Company A');
  });

  it('should update searchedCompanies when search term changes', () => {
    const event = { target: { value: 'Company A' } } as unknown as Event;
    
    component.onInputChange(event);
    
    expect(component.searchTerm).toBe('company a');
    expect(component.searchedCompanies.length).toBe(1);
    expect(component.searchedCompanies[0].name).toBe('Company A');
  });

  it('should set isLoader true while loading and false after companies are loaded', () => {
    // Arrange
    repositoryConfigServiceSpy.getPortfolioCompanies.and.callFake(() => {
      expect(component.isLoader).toBeTrue(); // Should be true before data arrives
      return of(mockCompanies);
    });

    // Act
    component.getCompanies();

    // Assert
    expect(component.isLoader).toBeFalse();
  });

  it('should handle error in getCompanies and set isLoader to false', () => {
    // Arrange
    const error = new Error('Network error');
    repositoryConfigServiceSpy.getPortfolioCompanies.and.returnValue({
      subscribe: (success: any, fail: any) => fail(error)
    } as any);

    // Act
    component.getCompanies();

    // Assert
    expect(component.isLoader).toBeFalse();
  });

  it('should emit selected company and deselect others on toggleCompanySelection', () => {
    // Arrange
    component.companies = [
      { name: 'A', companyId: 1, selected: false },
      { name: 'B', companyId: 2, selected: false }
    ];
    const company = component.companies[1];

    // Act
    component.toggleCompanySelection(company);

    // Assert
    expect(component.companies[0].selected).toBeFalse();
    expect(component.companies[1].selected).toBeTrue();
    expect(component.selectedCompanies.emit).toHaveBeenCalledWith([company]);
  });

  it('should handle empty companies list gracefully', () => {
    component.companies = [];
    expect(component.filteredCompanies.length).toBe(0);
    expect(() => component.toggleCompanySelection({})).not.toThrow();
  });

  it('should return all companies if searchTerm is empty', () => {
    component.searchTerm = '';
    expect(component.filteredCompanies.length).toBe(component.companies.length);
  });

  it('should return empty filteredCompanies if searchTerm matches no company', () => {
    component.searchTerm = 'Nonexistent';
    expect(component.filteredCompanies.length).toBe(0);
  });

  it('should handle companies with duplicate names', () => {
    component.companies = [
      { name: 'Dup', companyId: 1, selected: false },
      { name: 'Dup', companyId: 2, selected: false }
    ];
    component.searchTerm = 'Dup';
    expect(component.filteredCompanies.length).toBe(2);
  });

  // Edge: onInputChange with whitespace and case
  it('should trim and lowercase searchTerm in onInputChange', () => {
    const event = { target: { value: '  Company B  ' } } as unknown as Event;
    component.onInputChange(event);
    expect(component.searchTerm).toBe('company b');
  });

  // Edge: toggleCompanySelection with company not in list
  it('should not throw if toggling selection for a company not in companies', () => {
    expect(() => component.toggleCompanySelection({ name: 'X', companyId: 99, selected: false })).not.toThrow();
  });
});