import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, Observable } from 'rxjs';
import { DashboardTrackerComponent } from './dashboard-tracker.component';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { GridDataResult } from '@progress/kendo-angular-grid';

class MockDashboardTrackerService {
  response: any = {
    data: [{ id: 1, name: 'Test' }],
    columns: [{ field: 'id' }, { field: 'name' }],
    totalRecords: 1
  };
  getDashboardTableData() {
    return of(this.response);
  }
}
describe('DashboardTrackerComponent', () => {
  let component: DashboardTrackerComponent;
  let fixture: ComponentFixture<DashboardTrackerComponent>;
  let dashboardTrackerService: MockDashboardTrackerService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DashboardTrackerComponent],
      providers: [
        { provide: DashboardTrackerService, useClass: MockDashboardTrackerService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardTrackerComponent);
    component = fixture.componentInstance;
    dashboardTrackerService = TestBed.inject(DashboardTrackerService) as any;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default property values', () => {
    expect(component.passedClass).toBe('');
    expect(component.isDashboardConfigurationTab).toBe(false);
    expect(component.isLoading).toBe(true);
    expect(component.moreColumn).toBe(false);
    expect(component.gridColumns).toEqual([]);
    expect(component.totalRecords).toBe(0);
    expect(component.state).toEqual({ skip: 0, take: 100 });
    expect(component.view).toBeUndefined();
  });

  it('should load data and columns on ngOnInit (positive scenario)', (done) => {
    fixture.detectChanges(); // triggers ngOnInit
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([{ id: 1, name: 'Test' }]);
      expect(result.total).toBe(1);
      expect(component.gridColumns).toEqual([{ field: 'id' }, { field: 'name' }]);
      expect(component.isLoading).toBe(false);
      done();
    });
  });

  it('should set totalRecords to 0 if not present in response', (done) => {
    dashboardTrackerService.response = {
      data: [{ id: 2 }],
      columns: [{ field: 'id' }]
    };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(component.totalRecords).toBe(0);
      expect(result.total).toBe(0);
      done();
    });
  });

  it('should handle null response (negative scenario)', (done) => {
    spyOn(dashboardTrackerService, 'getDashboardTableData').and.returnValue(of(null));
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      expect(component.isLoading).toBe(false);
      done();
    });
  });

  it('should handle response with missing data or columns (edge case)', (done) => {
    dashboardTrackerService.response = { foo: 'bar' };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      done();
    });
  });

  it('should handle empty data and columns (edge case)', (done) => {
    dashboardTrackerService.response = { data: [], columns: [], totalRecords: 0 };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      done();
    });
  });

  it('should update state and reload data on dataStateChange', (done) => {
    fixture.detectChanges();
    const newState = { skip: 10, take: 10 };
    spyOn(component, 'loadDashboardTableData').and.callThrough();
    component.dataStateChange(newState);
    expect(component.state).toEqual(newState);
    expect(component.loadDashboardTableData).toHaveBeenCalledWith(newState);
    // Check observable emits after state change
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([{ id: 1, name: 'Test' }]);
      done();
    });
  });

  it('should set isLoading true while loading and false after', (done) => {
    let isLoadingDuringCall = undefined;
    spyOn(dashboardTrackerService, 'getDashboardTableData').and.callFake(() => {
      isLoadingDuringCall = component.isLoading;
      return of({ data: [], columns: [] });
    });
    component.loadDashboardTableData({ skip: 0, take: 1 });
    expect(isLoadingDuringCall).toBe(true);
    setTimeout(() => {
      expect(component.isLoading).toBe(false);
      done();
    }, 0);
  });

  it('should call navigateToDashboardConfig without error', () => {
    expect(() => component.navigateToDashboardConfig()).not.toThrow();
  });

  it('should handle rapid consecutive dataStateChange calls (edge case)', (done) => {
    fixture.detectChanges();
    const states = [
      { skip: 0, take: 5 },
      { skip: 5, take: 5 },
      { skip: 10, take: 5 }
    ];
    let callCount = 0;
    spyOn(component, 'loadDashboardTableData').and.callFake(() => {
      callCount++;
      component.view = of({ data: [], total: 0 });
    });
    states.forEach(s => component.dataStateChange(s));
    expect(callCount).toBe(states.length);
    done();
  });

  // Add more tests as needed for future methods or logic
});
