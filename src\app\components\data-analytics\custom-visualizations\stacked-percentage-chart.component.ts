import { ChangeDetectorRef, Component, <PERSON>ementRef, OnInit, ViewChild } from "@angular/core";
import * as Highcharts from "highcharts";

declare global {
  interface Window {
    revealBridge: any;
    revealBridgeListener: any;
  }
}

@Component({
  selector: "app-stacked-percentage-chart",
  templateUrl: "./stacked-percentage-chart.component.html",
  styleUrls: ["./stacked-percentage-chart.component.scss"],
})
export class StackedPercentageChartComponent implements OnInit {
  data: any = [];
  headers: any = [];
  @ViewChild("StackedPercentageColumnChart", { static: true })
  chartContainer: ElementRef;

  Highcharts: typeof Highcharts = Highcharts;
  chartOptions: Highcharts.Options;
  constructor(private ref: ChangeDetectorRef) { }

  /**
   * Initializes the component and sets up the `revealBridge` object on the `window` for communication with the host.
   * 
   * The `revealBridge` object provides the following methods:
   * - `sendMessageToHost(data)`: Sends a message to the host using an iframe or `postMessage`.
   * - `notifyExtensionIsReady(formatting)`: Notifies the host that the extension is ready, optionally with formatting.
   * - `runAction(actionName, data)`: Sends a message to the host to run a specified action with provided data.
   * - `openUrl(url)`: Sends a message to the host to open a specified URL.
   * 
   * Additionally, the `processMessageFromHost` function is defined to handle incoming messages from the host.
   * It validates the message and calls `dataReady` on the `revealBridgeListener` if the message is valid.
   * 
   * The `revealBridgeListener` object is also set up on the `window` to handle data readiness.
   * It provides the `dataReady` method which processes incoming data, updates the component's data, creates a chart, and triggers change detection.
   * 
   * Finally, the `notifyExtensionIsReady` method is called to notify the host that the extension is ready.
   */
  ngOnInit(): void {
    window.revealBridge = {
      sendMessageToHost: function (data) {
        try {
          var iframe = document.createElement("IFRAME");
          var message = encodeURIComponent(JSON.stringify(data));
          iframe.setAttribute("src", "js-frame:" + message);
          document.documentElement.appendChild(iframe);
          iframe.parentNode.removeChild(iframe);
          iframe = null;
        } catch (e) { }

        try {
          if (window.top && window.top.location) {
            window.top.postMessage(data, window.top.location.origin);
          }
        } catch (e) { }
      },

      notifyExtensionIsReady: function (formatting) {
        if (formatting) {
          this.sendMessageToHost({ message: "ready", formatting: true });
        } else {
          this.sendMessageToHost({ message: "ready" });
        }
      },

      runAction: function (actionName, data) {
        this.sendMessageToHost({
          message: "runAction",
          action: actionName,
          rowData: data,
        });
      },

      openUrl: function (url) {
        this.sendMessageToHost({ message: "openURL", URL: url });
      },
    };

    function processMessageFromHost(message): void {
      const trustedOrigin = window.top.location.origin;
      if (message.origin !== trustedOrigin) {
        console.warn(
          `Rejected message from untrusted origin: ${message.origin}. Expected: ${trustedOrigin}`
        );
        return;
      }
      if (!message?.data?.metadata || message.data.message) {
        console.log('Invalid message or message contains a direct message.');
        return;
      }
    
      if (!window.revealBridgeListener) {
        console.log('revealBridgeListener is not defined.');
        return;
      }
    
      console.log('Processing message:', message);
      window.revealBridgeListener.dataReady(message.data);
    }

    window.addEventListener("message", processMessageFromHost, false);

    window.revealBridgeListener = {
      dataReady: (incomingData: any) => {
        this.data = this.dataToJson(incomingData);
        this.createChart(this.data, incomingData);
        this.ref.detectChanges();
      },
    };
    window.revealBridge.notifyExtensionIsReady();
  }

  /**
   * Converts the provided data into an array of JSON objects.
   * 
   * @param data - The data to be converted, which should contain metadata and data arrays.
   * @returns An array of JSON objects where each object represents a row of the input data.
   * 
   * The input data is expected to have the following structure:
   * {
   *   metadata: {
   *     columns: [
   *       { name: string },
   *       ...
   *     ]
   *   },
   *   data: [
   *     [value1, value2, ...],
   *     ...
   *   ]
   * }
   * 
   * Each column name in the metadata.columns array will be used as a key in the resulting JSON objects,
   * and the corresponding values from the data array will be assigned to these keys.
   * 
   * If the metadata.columns array is not present, an empty array is returned.
   */
  dataToJson(data: any) {
    let propertyNames = [];

    if (!data.metadata.columns) {
      return [];
    }
    for (var c = 0; c < data.metadata.columns.length; c++) {
      var column = data.metadata.columns[c];
      propertyNames.push(column.name);
    }

    let dataObjects = [];
    for (var i = 0; i < data.data.length; i++) {
      var rowData = data.data[i];
      let dataObject: any = {};
      for (var j = 0; j < rowData.length; j++) {
        dataObject[propertyNames[j]] = rowData[j];
      }
      dataObjects.push(dataObject);
    }

    return dataObjects;
  }

  /**
   * Calculates the percentage of a given value relative to the total of specified series in a data object.
   *
   * @param value - The value for which the percentage is to be calculated.
   * @param dataObject - An object containing numerical values for different series.
   * @param seriesNames - An array of keys representing the series to be considered in the total calculation.
   * @returns The percentage of the value relative to the total of the specified series, rounded to two decimal places.
   *          Returns 0 if the total of the specified series is 0 to handle division by zero.
   */
  getPercentage = (value: number, dataObject: { [key: string]: number }, seriesNames: string[]): number => {
    const total = seriesNames.reduce((acc, key) => acc + (dataObject[key] || 0), 0);

    if (total === 0) {
      return 0; // Handle division by zero
    }

    return parseFloat(((value / total) * 100).toFixed(2));
  };

  /**
   * Creates a stacked percentage column chart using Highcharts.
   *
   * @param dataObjects - An array of data objects to be visualized.
   * @param data - Metadata and other relevant information for the chart.
   *
   * The function processes the input data to extract categories and series data,
   * then configures the chart options including the type, axes, title, tooltip,
   * plot options, and series data. Finally, it renders the chart in the specified
   * container.
   *
   * The chart displays data in a stacked column format where each column represents
   * a category and the segments within the column represent different series. The
   * segments are displayed as percentages of the total column height.
   */
  createChart = (dataObjects: any, data: any) => {
    const filteredData = data.metadata.columns.filter((item) => item.type === 0);
    const yAxisItem = filteredData.length > 0 ? filteredData[0] : null;
    const categories = dataObjects.map((item: any) => item[yAxisItem?.name]);
    const seriesNames = Object.keys(dataObjects[0]).filter(
      (key) => key !== yAxisItem?.name
    );
    const seriesData = seriesNames.map((name) => {
      const values = dataObjects.map((item: any) => item[name]);
      return {
        name: name,
        data: values.map((val, index) => ({
          y: this.getPercentage(val, dataObjects[index], seriesNames),
          actual: val
        })),
      };
    });
    // Flatten the data points to get an array of all y values
    const allYValues = seriesData.flatMap(series => series.data.map(point => point.y));

    // Calculate the minimum and maximum values
    const minValue = Math.min(...allYValues);
    const maxValue = Math.max(...allYValues);
    this.chartOptions = {
      chart: {
        type: "column",
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: categories,
      },
      title: {
        text: "",
        style: {
          display: "none",
        },
      },
      subtitle: {
        text: "",
        style: {
          display: "none",
        },
      },
      yAxis: {
        title: {
          text: "Percent",
        },
        stackLabels: {
          enabled: false,
        },
        min: minValue,
        max: maxValue,
      },
      tooltip: {
        shared: true,
        formatter: function () {
          let tooltip = `<b>${this.x}</b><br/>`;
          this.points.forEach(point => {
            tooltip += `<span style="color:${point.series.color}">${point.series.name}</span>: <b>${point.point['actual']}</b> (${point.point.y.toFixed(2)}%)<br/>`;
          });
          return tooltip;
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal', // or 'normal' if you want normal stacking
        dataLabels: {
          enabled: true,
          format: '{point.y:.2f}%',
        },
        pointPadding: 0.1, // Adjust padding between columns
        groupPadding: 0.1, // Adjust padding between groups of columns
        borderWidth: 1, // Set border width of columns
        },
        series: {
          dataLabels: {
              enabled: true,
              style: {
                  fontWeight: 'bold',
                  color: 'black'
              }
          }
      }
      },
      series: seriesData as Highcharts.SeriesColumnOptions[],
      accessibility: {
        enabled: false,
      },
    };

    Highcharts.chart(this.chartContainer.nativeElement, this.chartOptions);
  };
}
