import { Inject, Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { DashboardTrackerConfigDto, SaveDashboardCellValuesDto } from "../components/dashboard-tracker/model/dashboard-tracker-config.model";

@Injectable({
  providedIn: "root",
})
export class DashboardTrackerService {
  myAppUrl: string = "";
  constructor(
    private readonly http: HttpClient,
    @Inject("BASE_URL") baseUrl: string
  ) {
    this.myAppUrl = baseUrl;
  }

  errorHandler(error: any) {
    return throwError(() => error);
  }

  getDashboardData(): Observable<any> {
    return this.http.get<any>(this.myAppUrl + "api/dashboard-tracker/get").pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  saveDashboardTrackerConfig(config: DashboardTrackerConfigDto): Observable<any> {
    return this.http.post<any>(this.myAppUrl + "api/dashboard-tracker/config/save", config).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getDashboardTableData(filter?: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + "api/dashboard-tracker/table-data", filter || {}).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  saveTrackerDropdownValues(dto: { trackerFieldId: number; dropdownValues?: string[] }): Observable<any> {
    return this.http.post<any>(
      this.myAppUrl + "api/dashboard-tracker/save-dropdown-values",
      dto
    ).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  getDashboardTrackerColumnData(): Observable<any> {
    return this.http.get<any>(this.myAppUrl + "api/dashboard-tracker/get-column-data").pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  saveDashboardCellValues(cellValues: SaveDashboardCellValuesDto): Observable<any> {
    return this.http.post<any>(this.myAppUrl + "api/dashboard-tracker/save-cell-values", cellValues).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
}
